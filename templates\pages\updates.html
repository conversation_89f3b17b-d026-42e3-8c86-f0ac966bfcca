{% extends "base.html" %} {% block content %}
<div class="updates-section">
  <div class="updates-container">
    <div class="header-content">
      <h1 data-translate="updatesTitle"></h1>
      <div class="description" data-translate="updatesDescription"></div>
    </div>

    <div class="updates-grid">
      {% for update in updates %}
      <div class="update-card">
        <div class="card-header">
          <i class="fas fa-newspaper"></i>
          <h2>{{ update['title'] }}</h2>
        </div>
        <div class="version-badge">{{ update['version'] }}</div>
        <div class="update-content">
          <p class="update-text">{{ update['content']|safe }}</p>{% if update['preview_link'] %}
          <div class="update-actions">
            <a
              href="{{ update['preview_link'] }}"
              target="_blank"
              class="update-preview"
            >
              <span data-translate="viewPreview"></span>
            </a>
          </div>
          {% endif %}
        </div>
      </div>
      {% endfor %} {% if not updates %}
      <div class="update-card">
        <div class="card-header">
          <i class="fas fa-exclamation-circle"></i>
          <h2>Sem Atualizações</h2>
        </div>
        <div class="update-content">
          <p>Não há atualizações disponíveis no momento.</p>
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %} {% block scripts %}
<script src="{{ url_for('static', filename='js/utils/UpdateManager.js') }}"></script>
{% endblock %}
