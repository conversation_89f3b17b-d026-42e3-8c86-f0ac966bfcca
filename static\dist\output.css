/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@layer properties;
:root, [data-theme="light"] {
  scrollbar-color: var(--color-gray-950) var(--color-gray-100);
  scrollbar-width: thin;
  overflow: auto;
  scroll-behavior: smooth;
  --background-primary: var(--color-gray-100);
  --background-secondary: var(--color-gray-200);
  --text-primary: var(--color-gray-950);
  --text-secondary: var(--color-gray-600);
  --accent-color: var(--is-red-50);
  --border-color: var(--color-gray-200);
  --shadow-color: hsla(0, 50%, 80%, 0.3);
  --is-red-95: oklch(0.95 0.02 29);
  --is-red-90: oklch(0.90 0.04 29);
  --is-red-85: oklch(0.85 0.06 29);
  --is-red-80: oklch(0.80 0.08 29);
  --is-red-75: oklch(0.75 0.10 29);
  --is-red-70: oklch(0.70 0.12 29);
  --is-red-65: oklch(0.65 0.14 29);
  --is-red-60: oklch(0.60 0.16 29);
  --is-red-55: oklch(0.55 0.17 29);
  --is-red-50: oklch(0.63 0.18 29);
  --is-red-45: oklch(0.58 0.17 29);
  --is-red-40: oklch(0.53 0.16 29);
  --is-red-35: oklch(0.48 0.14 29);
  --is-red-30: oklch(0.43 0.12 29);
  --is-red-25: oklch(0.38 0.10 29);
  --is-red-20: oklch(0.33 0.08 29);
  --is-red-15: oklch(0.28 0.06 29);
  --is-red-10: oklch(0.23 0.04 29);
  --is-red-5: oklch(0.18 0.02 29);
}
[data-theme="dark"] {
  scrollbar-color: var(--color-gray-300) var(--color-gray-950);
  --background-primary: var(--color-gray-950);
  --background-secondary: var(--color-gray-900);
  --text-primary: var(--color-gray-100);
  --text-secondary: var(--color-gray-300);
  --accent-color: var(--is-red-40);
  --border-color: var(--color-gray-900);
  --shadow-color: hsla(240, 100%, 20%, 0.3);
}
[data-theme="black"] {
  scrollbar-color: white black;
  --background-primary: black;
  --background-secondary: var(--color-neutral-950);
  --text-primary: white;
  --text-secondary: var(--color-neutral-300);
  --accent-color: var(--is-red-50);
  --border-color: var(--color-neutral-900);
  --shadow-color: hsla(0, 100%, 15%, 0.3);
}
[data-theme="red"] {
  scrollbar-color: var(--is-red-70) var(--is-red-15);
  --background-primary: var(--is-red-15);
  --background-secondary: var(--is-red-20);
  --text-primary: var(--is-red-90);
  --text-secondary: var(--is-red-70);
  --accent-color: var(--is-red-50);
  --border-color: var(--is-red-25);
  --shadow-color: hsla(0, 100%, 27%, 0.2);
}
body {
  font-family: 'Poppins', sans-serif;
  background-color: var(--background-primary);
  color: var(--text-primary);
}
.active {
  --tw-font-weight: var(--font-weight-bold);
  font-weight: var(--font-weight-bold);
  color: var(--text-secondary);
}
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes slideRight {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}
@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
@keyframes float {
  0% {
    transform: translatey(0px);
  }
  50% {
    transform: translatey(-20px);
  }
  100% {
    transform: translatey(0px);
  }
}
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-50: #FEF2F2;
    --color-red-100: #FEE2E2;
    --color-red-200: #FECACA;
    --color-red-300: #FCA5A5;
    --color-red-400: #F87171;
    --color-red-500: #EF4444;
    --color-red-600: #DC2626;
    --color-red-700: #B91C1C;
    --color-red-800: #991B1B;
    --color-red-900: #7F1D1D;
    --color-orange-50: #FFFAF0;
    --color-orange-100: #FEEBC8;
    --color-orange-200: #FBD38D;
    --color-orange-300: #F6AD55;
    --color-orange-400: #ED8936;
    --color-orange-500: #DD6B20;
    --color-orange-600: #C05621;
    --color-orange-700: #9C4221;
    --color-orange-800: #7B341E;
    --color-orange-900: #652B19;
    --color-yellow-50: #FFFBEB;
    --color-yellow-100: #FEF3C7;
    --color-yellow-200: #FDE68A;
    --color-yellow-300: #FCD34D;
    --color-yellow-400: #FBBF24;
    --color-yellow-500: #F59E0B;
    --color-yellow-600: #D97706;
    --color-yellow-700: #B45309;
    --color-yellow-800: #92400E;
    --color-yellow-900: #78350F;
    --color-lime-200: oklch(93.8% 0.127 124.321);
    --color-lime-300: oklch(89.7% 0.196 126.665);
    --color-lime-400: oklch(84.1% 0.238 128.85);
    --color-lime-500: oklch(76.8% 0.233 130.85);
    --color-lime-800: oklch(45.3% 0.124 130.933);
    --color-green-50: #ECFDF5;
    --color-green-100: #D1FAE5;
    --color-green-200: #A7F3D0;
    --color-green-300: #6EE7B7;
    --color-green-400: #34D399;
    --color-green-500: #10B981;
    --color-green-600: #059669;
    --color-green-700: #047857;
    --color-green-800: #065F46;
    --color-green-900: #064E3B;
    --color-emerald-600: oklch(59.6% 0.145 163.225);
    --color-teal-50: #F0FDFA;
    --color-teal-100: #CCFBF1;
    --color-teal-200: #99F6E4;
    --color-teal-300: #5EEAD4;
    --color-teal-400: #2DD4BF;
    --color-teal-500: #14B8A6;
    --color-teal-600: #0D9488;
    --color-teal-700: #0F766E;
    --color-teal-800: #115E59;
    --color-cyan-200: #A5F3FC;
    --color-cyan-300: #67E8F9;
    --color-cyan-400: #22D3EE;
    --color-cyan-500: #06B6D4;
    --color-cyan-600: #0891B2;
    --color-cyan-800: #155E75;
    --color-sky-400: oklch(74.6% 0.16 232.661);
    --color-sky-500: oklch(68.5% 0.169 237.323);
    --color-sky-600: oklch(58.8% 0.158 241.966);
    --color-blue-50: #EFF6FF;
    --color-blue-100: #DBEAFE;
    --color-blue-200: #BFDBFE;
    --color-blue-300: #93C5FD;
    --color-blue-400: #60A5FA;
    --color-blue-500: #3B82F6;
    --color-blue-600: #2563EB;
    --color-blue-700: #1D4ED8;
    --color-blue-800: #1E40AF;
    --color-blue-900: #1E3A8A;
    --color-indigo-50: #EEF2FF;
    --color-indigo-100: #E0E7FF;
    --color-indigo-200: #C7D2FE;
    --color-indigo-300: #A5B4FC;
    --color-indigo-400: #818CF8;
    --color-indigo-500: #6366F1;
    --color-indigo-600: #4F46E5;
    --color-indigo-700: #4338CA;
    --color-indigo-800: #3730A3;
    --color-indigo-900: #312E81;
    --color-purple-50: #F5F3FF;
    --color-purple-100: #EDE9FE;
    --color-purple-200: #DDD6FE;
    --color-purple-300: #C4B5FD;
    --color-purple-400: #A78BFA;
    --color-purple-500: #8B5CF6;
    --color-purple-600: #7C3AED;
    --color-purple-700: #6D28D9;
    --color-purple-800: #5B21B6;
    --color-purple-900: #4C1D95;
    --color-pink-50: #FDF2F8;
    --color-pink-100: #FCE7F3;
    --color-pink-200: #FBCFE8;
    --color-pink-300: #F9A8D4;
    --color-pink-400: #F472B6;
    --color-pink-500: #EC4899;
    --color-pink-600: #DB2777;
    --color-pink-700: #BE185D;
    --color-pink-800: #9D174D;
    --color-pink-900: #831843;
    --color-gray-50: #F9FAFB;
    --color-gray-100: #F3F4F6;
    --color-gray-200: #E5E7EB;
    --color-gray-300: #D1D5DB;
    --color-gray-400: #9CA3AF;
    --color-gray-500: #6B7280;
    --color-gray-600: #4B5563;
    --color-gray-700: #374151;
    --color-gray-800: #1F2937;
    --color-gray-900: #111827;
    --color-gray-950: oklch(13% 0.028 261.692);
    --color-neutral-300: oklch(87% 0 0);
    --color-neutral-400: oklch(70.8% 0 0);
    --color-neutral-900: oklch(20.5% 0 0);
    --color-neutral-950: oklch(14.5% 0 0);
    --color-stone-400: oklch(70.9% 0.01 56.259);
    --color-black: #000000;
    --color-white: #ffffff;
    --spacing: 0.25rem;
    --breakpoint-md: 48rem;
    --breakpoint-lg: 64rem;
    --breakpoint-xl: 80rem;
    --container-2xs: 18rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --text-8xl: 6rem;
    --text-8xl--line-height: 1;
    --text-9xl: 8rem;
    --text-9xl--line-height: 1;
    --font-weight-thin: 100;
    --font-weight-extralight: 200;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;
    --tracking-tighter: -0.05em;
    --tracking-tight: -0.025em;
    --tracking-normal: 0em;
    --tracking-wide: 0.025em;
    --tracking-wider: 0.05em;
    --tracking-widest: 0.1em;
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-3xl: 1.5rem;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --blur-xs: 4px;
    --blur-sm: 8px;
    --aspect-video: 16 / 9;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }
  .collapse {
    visibility: collapse;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .datatable-wrapper {
    width: 100%;
    & .datatable-top {
      display: flex;
      justify-content: space-between;
      flex-direction: column-reverse;
      align-items: start;
      gap: 1rem;
      margin-bottom: 1rem;
      @media (min-width: 640px) {
        flex-direction: row-reverse;
        align-items: center;
      }
    }
    & .datatable-search .datatable-input {
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      background-color: var(--color-gray-50);
      min-width: 16rem;
    }
    & .datatable-input {
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      background-color: var(--color-gray-50);
      min-width: 16rem;
    }
    .dark & .datatable-search .datatable-input {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    .dark & .datatable-input {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    & thead th .datatable-input {
      background-color: white;
      font-weight: 400;
      color: var(--color-gray-900);
      padding-top: .35rem;
      padding-bottom: .35rem;
      min-width: 0;
    }
    .dark & thead th .datatable-input {
      background-color: var(--color-gray-700);
      border-color: var(--color-gray-600);
      color: white;
    }
    & .datatable-top .datatable-dropdown {
      color: var(--color-gray-500);
      font-size: 0.875rem;
    }
    .dark & .datatable-top .datatable-dropdown {
      color: var(--color-gray-400);
    }
    & .datatable-top .datatable-dropdown .datatable-selector {
      background-color: var(--color-gray-50);
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      margin-right: 0.25rem;
      min-width: 4rem;
    }
    .dark & .datatable-top .datatable-dropdown .datatable-selector {
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
      color: white;
    }
    & .datatable-container thead tr.search-filtering-row th {
      padding-top: 0;
    }
    & .datatable-search .datatable-input:focus {
      border-color: var(--color-blue-600);
    }
    & .datatable-container {
      overflow-x: auto;
    }
    & .datatable-table {
      width: 100%;
      font-size: 0.875rem;
      color: var(--color-gray-500);
      text-align: left;
    }
    .dark & .datatable-table {
      color: var(--color-gray-400);
    }
    & .datatable-table thead {
      font-size: 0.75rem;
      color: var(--color-gray-500);
      background-color: var(--color-gray-50);
    }
    .dark & .datatable-table thead {
      color: var(--color-gray-400);
      background-color: var(--color-gray-800);
    }
    & .datatable-table thead th {
      white-space: nowrap;
    }
    & .datatable-table thead th {
      width: auto !important;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    & .datatable-table tbody th {
      width: auto !important;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    & .datatable-table tbody td {
      width: auto !important;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    & .datatable-table thead th .datatable-sorter {
      text-transform: uppercase;
    }
    & .datatable-table thead th {
      text-transform: uppercase;
    }
    & .datatable-table thead th .datatable-sorter:hover {
      color: var(--color-gray-900);
    }
    & .datatable-table thead th.datatable-ascending .datatable-sorter {
      color: var(--color-gray-900);
    }
    & .datatable-table thead th.datatable-descending .datatable-sorter {
      color: var(--color-gray-900);
    }
    .dark & .datatable-table thead th .datatable-sorter:hover {
      color: white;
    }
    .dark & .datatable-table thead th.datatable-ascending .datatable-sorter {
      color: white;
    }
    .dark & .datatable-table thead th.datatable-descending .datatable-sorter {
      color: white;
    }
    & .datatable-table tbody tr.selected {
      background-color: var(--color-gray-100);
    }
    .dark & .datatable-table tbody tr.selected {
      background-color: var(--color-gray-700);
    }
    & .datatable-table tbody tr {
      border-bottom: 1px solid var(--color-gray-200);
    }
    .dark & .datatable-table tbody tr {
      border-bottom: 1px solid var(--color-gray-700);
    }
    & .datatable-table .datatable-empty {
      text-align: center;
    }
    & .datatable-bottom {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: start;
      margin-top: 1rem;
      gap: 1rem;
      @media (min-width: 640px) {
        flex-direction: row;
        align-items: center;
      }
    }
    & .datatable-bottom .datatable-info {
      color: var(--color-gray-500);
      font-size: 0.875rem;
    }
    .dark & .datatable-bottom .datatable-info {
      color: var(--color-gray-400);
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list {
      display: flex;
      align-items: center;
      height: 2rem;
      font-size: 0.875rem;
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link {
      display: flex;
      align-items: center;
      color: var(--color-gray-500);
      font-weight: 500;
      padding-left: 0.75rem;
      padding-right: 0.75rem;
      height: 2rem;
      font-size: 0.875rem;
      border-top: 1px solid var(--color-gray-300);
      border-bottom: 1px solid var(--color-gray-300);
      border-right: 1px solid var(--color-gray-300);
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link {
      color: var(--color-gray-400);
      border-color: var(--color-gray-700);
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type {
      position: relative;
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type {
      position: relative;
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      left: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(-50%, -50%);
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%23111827' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%239CA3AF' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      right: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(50%, -50%);
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%23111827' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%239CA3AF' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-top-left-radius: 0.5rem;
      border-bottom-left-radius: 0.5rem;
      border-left: 1px solid var(--color-gray-300);
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-left: 1px solid var(--color-gray-700);
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      border-top-right-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
      border-left: 0;
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-50);
      color: var(--color-gray-700);
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-700);
      color: white;
    }
  }
  .datatable-bottom {
    .datatable-wrapper & {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: start;
      margin-top: 1rem;
      gap: 1rem;
      @media (min-width: 640px) {
        flex-direction: row;
        align-items: center;
      }
    }
    .datatable-wrapper & .datatable-info {
      color: var(--color-gray-500);
      font-size: 0.875rem;
    }
    .dark .datatable-wrapper & .datatable-info {
      color: var(--color-gray-400);
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list {
      display: flex;
      align-items: center;
      height: 2rem;
      font-size: 0.875rem;
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item-link {
      display: flex;
      align-items: center;
      color: var(--color-gray-500);
      font-weight: 500;
      padding-left: 0.75rem;
      padding-right: 0.75rem;
      height: 2rem;
      font-size: 0.875rem;
      border-top: 1px solid var(--color-gray-300);
      border-bottom: 1px solid var(--color-gray-300);
      border-right: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item-link {
      color: var(--color-gray-400);
      border-color: var(--color-gray-700);
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type {
      position: relative;
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type {
      position: relative;
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      left: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(-50%, -50%);
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%23111827' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%239CA3AF' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      right: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(50%, -50%);
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%23111827' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%239CA3AF' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-top-left-radius: 0.5rem;
      border-bottom-left-radius: 0.5rem;
      border-left: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-left: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      border-top-right-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
      border-left: 0;
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-50);
      color: var(--color-gray-700);
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-700);
      color: white;
    }
  }
  .datatable-pagination {
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list {
      display: flex;
      align-items: center;
      height: 2rem;
      font-size: 0.875rem;
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item-link {
      display: flex;
      align-items: center;
      color: var(--color-gray-500);
      font-weight: 500;
      padding-left: 0.75rem;
      padding-right: 0.75rem;
      height: 2rem;
      font-size: 0.875rem;
      border-top: 1px solid var(--color-gray-300);
      border-bottom: 1px solid var(--color-gray-300);
      border-right: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item-link {
      color: var(--color-gray-400);
      border-color: var(--color-gray-700);
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type {
      position: relative;
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type {
      position: relative;
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      left: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(-50%, -50%);
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%23111827' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%239CA3AF' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      right: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(50%, -50%);
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%23111827' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%239CA3AF' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-top-left-radius: 0.5rem;
      border-bottom-left-radius: 0.5rem;
      border-left: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-left: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      border-top-right-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
      border-left: 0;
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-50);
      color: var(--color-gray-700);
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-700);
      color: white;
    }
  }
  .datatable-pagination-list-item-link {
    .datatable-wrapper .datatable-bottom .datatable-pagination & {
      display: flex;
      align-items: center;
      color: var(--color-gray-500);
      font-weight: 500;
      padding-left: 0.75rem;
      padding-right: 0.75rem;
      height: 2rem;
      font-size: 0.875rem;
      border-top: 1px solid var(--color-gray-300);
      border-bottom: 1px solid var(--color-gray-300);
      border-right: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination & {
      color: var(--color-gray-400);
      border-color: var(--color-gray-700);
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type & {
      color: transparent;
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type & {
      color: transparent;
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type & {
      color: transparent;
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type & {
      color: transparent;
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type &::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      left: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(-50%, -50%);
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type &:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%23111827' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type &::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%239CA3AF' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type &:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type &::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      right: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(50%, -50%);
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type &:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%23111827' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type &::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%239CA3AF' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type &:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type & {
      border-top-left-radius: 0.5rem;
      border-bottom-left-radius: 0.5rem;
      border-left: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type & {
      border-left: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type & {
      border-top-right-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
      border-left: 0;
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination &:hover {
      background-color: var(--color-gray-50);
      color: var(--color-gray-700);
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination &:hover {
      background-color: var(--color-gray-700);
      color: white;
    }
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .start-0 {
    inset-inline-start: calc(var(--spacing) * 0);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-\[60px\] {
    bottom: 60px;
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-30 {
    z-index: 30;
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .z-\[51\] {
    z-index: 51;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .m-10 {
    margin: calc(var(--spacing) * 10);
  }
  .m-44 {
    margin: calc(var(--spacing) * 44);
  }
  .m-50 {
    margin: calc(var(--spacing) * 50);
  }
  .m-52 {
    margin: calc(var(--spacing) * 52);
  }
  .m-53 {
    margin: calc(var(--spacing) * 53);
  }
  .m-54 {
    margin: calc(var(--spacing) * 54);
  }
  .m-55 {
    margin: calc(var(--spacing) * 55);
  }
  .m-56 {
    margin: calc(var(--spacing) * 56);
  }
  .m-57 {
    margin: calc(var(--spacing) * 57);
  }
  .m-60 {
    margin: calc(var(--spacing) * 60);
  }
  .m-62 {
    margin: calc(var(--spacing) * 62);
  }
  .m-65 {
    margin: calc(var(--spacing) * 65);
  }
  .m-68 {
    margin: calc(var(--spacing) * 68);
  }
  .m-75 {
    margin: calc(var(--spacing) * 75);
  }
  .m-76 {
    margin: calc(var(--spacing) * 76);
  }
  .m-82 {
    margin: calc(var(--spacing) * 82);
  }
  .m-85 {
    margin: calc(var(--spacing) * 85);
  }
  .m-88 {
    margin: calc(var(--spacing) * 88);
  }
  .m-93 {
    margin: calc(var(--spacing) * 93);
  }
  .m-98 {
    margin: calc(var(--spacing) * 98);
  }
  .m-104 {
    margin: calc(var(--spacing) * 104);
  }
  .m-106 {
    margin: calc(var(--spacing) * 106);
  }
  .m-115 {
    margin: calc(var(--spacing) * 115);
  }
  .m-122 {
    margin: calc(var(--spacing) * 122);
  }
  .m-124 {
    margin: calc(var(--spacing) * 124);
  }
  .m-130 {
    margin: calc(var(--spacing) * 130);
  }
  .m-134 {
    margin: calc(var(--spacing) * 134);
  }
  .m-155 {
    margin: calc(var(--spacing) * 155);
  }
  .m-268 {
    margin: calc(var(--spacing) * 268);
  }
  .m-305 {
    margin: calc(var(--spacing) * 305);
  }
  .m-350 {
    margin: calc(var(--spacing) * 350);
  }
  .m-357 {
    margin: calc(var(--spacing) * 357);
  }
  .m-360 {
    margin: calc(var(--spacing) * 360);
  }
  .m-365 {
    margin: calc(var(--spacing) * 365);
  }
  .m-399 {
    margin: calc(var(--spacing) * 399);
  }
  .m-420 {
    margin: calc(var(--spacing) * 420);
  }
  .m-443 {
    margin: calc(var(--spacing) * 443);
  }
  .m-480 {
    margin: calc(var(--spacing) * 480);
  }
  .m-484 {
    margin: calc(var(--spacing) * 484);
  }
  .m-530 {
    margin: calc(var(--spacing) * 530);
  }
  .m-591 {
    margin: calc(var(--spacing) * 591);
  }
  .m-619 {
    margin: calc(var(--spacing) * 619);
  }
  .m-644 {
    margin: calc(var(--spacing) * 644);
  }
  .m-654 {
    margin: calc(var(--spacing) * 654);
  }
  .m-665 {
    margin: calc(var(--spacing) * 665);
  }
  .m-672 {
    margin: calc(var(--spacing) * 672);
  }
  .m-684 {
    margin: calc(var(--spacing) * 684);
  }
  .m-690 {
    margin: calc(var(--spacing) * 690);
  }
  .m-691 {
    margin: calc(var(--spacing) * 691);
  }
  .m-707 {
    margin: calc(var(--spacing) * 707);
  }
  .m-740 {
    margin: calc(var(--spacing) * 740);
  }
  .m-765 {
    margin: calc(var(--spacing) * 765);
  }
  .m-801 {
    margin: calc(var(--spacing) * 801);
  }
  .m-946 {
    margin: calc(var(--spacing) * 946);
  }
  .m-950 {
    margin: calc(var(--spacing) * 950);
  }
  .m-966 {
    margin: calc(var(--spacing) * 966);
  }
  .m-967 {
    margin: calc(var(--spacing) * 967);
  }
  .m-1106 {
    margin: calc(var(--spacing) * 1106);
  }
  .m-1170 {
    margin: calc(var(--spacing) * 1170);
  }
  .m-1187 {
    margin: calc(var(--spacing) * 1187);
  }
  .m-1191 {
    margin: calc(var(--spacing) * 1191);
  }
  .m-1210 {
    margin: calc(var(--spacing) * 1210);
  }
  .m-1216 {
    margin: calc(var(--spacing) * 1216);
  }
  .m-1260 {
    margin: calc(var(--spacing) * 1260);
  }
  .m-1274 {
    margin: calc(var(--spacing) * 1274);
  }
  .m-1301 {
    margin: calc(var(--spacing) * 1301);
  }
  .m-1380 {
    margin: calc(var(--spacing) * 1380);
  }
  .m-1444 {
    margin: calc(var(--spacing) * 1444);
  }
  .m-1514 {
    margin: calc(var(--spacing) * 1514);
  }
  .m-1595 {
    margin: calc(var(--spacing) * 1595);
  }
  .m-1601 {
    margin: calc(var(--spacing) * 1601);
  }
  .m-1640 {
    margin: calc(var(--spacing) * 1640);
  }
  .m-1839 {
    margin: calc(var(--spacing) * 1839);
  }
  .m-1870 {
    margin: calc(var(--spacing) * 1870);
  }
  .m-1899 {
    margin: calc(var(--spacing) * 1899);
  }
  .m-1992 {
    margin: calc(var(--spacing) * 1992);
  }
  .m-2014 {
    margin: calc(var(--spacing) * 2014);
  }
  .m-2056 {
    margin: calc(var(--spacing) * 2056);
  }
  .m-2081 {
    margin: calc(var(--spacing) * 2081);
  }
  .m-2139 {
    margin: calc(var(--spacing) * 2139);
  }
  .m-2196 {
    margin: calc(var(--spacing) * 2196);
  }
  .m-2202 {
    margin: calc(var(--spacing) * 2202);
  }
  .m-2210 {
    margin: calc(var(--spacing) * 2210);
  }
  .m-2254 {
    margin: calc(var(--spacing) * 2254);
  }
  .m-2270 {
    margin: calc(var(--spacing) * 2270);
  }
  .m-2290 {
    margin: calc(var(--spacing) * 2290);
  }
  .m-2291 {
    margin: calc(var(--spacing) * 2291);
  }
  .m-2299 {
    margin: calc(var(--spacing) * 2299);
  }
  .m-2308 {
    margin: calc(var(--spacing) * 2308);
  }
  .m-2318 {
    margin: calc(var(--spacing) * 2318);
  }
  .m-2331 {
    margin: calc(var(--spacing) * 2331);
  }
  .m-2370 {
    margin: calc(var(--spacing) * 2370);
  }
  .m-2414 {
    margin: calc(var(--spacing) * 2414);
  }
  .m-2435 {
    margin: calc(var(--spacing) * 2435);
  }
  .m-2528 {
    margin: calc(var(--spacing) * 2528);
  }
  .m-2591 {
    margin: calc(var(--spacing) * 2591);
  }
  .m-2657 {
    margin: calc(var(--spacing) * 2657);
  }
  .m-2847 {
    margin: calc(var(--spacing) * 2847);
  }
  .m-2930 {
    margin: calc(var(--spacing) * 2930);
  }
  .m-2943 {
    margin: calc(var(--spacing) * 2943);
  }
  .m-3084 {
    margin: calc(var(--spacing) * 3084);
  }
  .m-3092 {
    margin: calc(var(--spacing) * 3092);
  }
  .m-3202 {
    margin: calc(var(--spacing) * 3202);
  }
  .m-3418 {
    margin: calc(var(--spacing) * 3418);
  }
  .m-3450 {
    margin: calc(var(--spacing) * 3450);
  }
  .m-3461 {
    margin: calc(var(--spacing) * 3461);
  }
  .m-3470 {
    margin: calc(var(--spacing) * 3470);
  }
  .m-3513 {
    margin: calc(var(--spacing) * 3513);
  }
  .m-3526 {
    margin: calc(var(--spacing) * 3526);
  }
  .m-3554 {
    margin: calc(var(--spacing) * 3554);
  }
  .m-3662 {
    margin: calc(var(--spacing) * 3662);
  }
  .m-3685 {
    margin: calc(var(--spacing) * 3685);
  }
  .m-3714 {
    margin: calc(var(--spacing) * 3714);
  }
  .m-3734 {
    margin: calc(var(--spacing) * 3734);
  }
  .m-3745 {
    margin: calc(var(--spacing) * 3745);
  }
  .m-3808 {
    margin: calc(var(--spacing) * 3808);
  }
  .m-3938 {
    margin: calc(var(--spacing) * 3938);
  }
  .m-4015 {
    margin: calc(var(--spacing) * 4015);
  }
  .m-4121 {
    margin: calc(var(--spacing) * 4121);
  }
  .m-4186 {
    margin: calc(var(--spacing) * 4186);
  }
  .m-4222 {
    margin: calc(var(--spacing) * 4222);
  }
  .m-4245 {
    margin: calc(var(--spacing) * 4245);
  }
  .m-4256 {
    margin: calc(var(--spacing) * 4256);
  }
  .m-4327 {
    margin: calc(var(--spacing) * 4327);
  }
  .m-4352 {
    margin: calc(var(--spacing) * 4352);
  }
  .m-4380 {
    margin: calc(var(--spacing) * 4380);
  }
  .m-4395 {
    margin: calc(var(--spacing) * 4395);
  }
  .m-4396 {
    margin: calc(var(--spacing) * 4396);
  }
  .m-4423 {
    margin: calc(var(--spacing) * 4423);
  }
  .m-4435 {
    margin: calc(var(--spacing) * 4435);
  }
  .m-4461 {
    margin: calc(var(--spacing) * 4461);
  }
  .m-4511 {
    margin: calc(var(--spacing) * 4511);
  }
  .m-4576 {
    margin: calc(var(--spacing) * 4576);
  }
  .m-4587 {
    margin: calc(var(--spacing) * 4587);
  }
  .m-4601 {
    margin: calc(var(--spacing) * 4601);
  }
  .m-4833 {
    margin: calc(var(--spacing) * 4833);
  }
  .m-4841 {
    margin: calc(var(--spacing) * 4841);
  }
  .m-4849 {
    margin: calc(var(--spacing) * 4849);
  }
  .m-4959 {
    margin: calc(var(--spacing) * 4959);
  }
  .m-5082 {
    margin: calc(var(--spacing) * 5082);
  }
  .m-5293 {
    margin: calc(var(--spacing) * 5293);
  }
  .m-5311 {
    margin: calc(var(--spacing) * 5311);
  }
  .m-5359 {
    margin: calc(var(--spacing) * 5359);
  }
  .m-5510 {
    margin: calc(var(--spacing) * 5510);
  }
  .m-6032 {
    margin: calc(var(--spacing) * 6032);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .me-2 {
    margin-inline-end: calc(var(--spacing) * 2);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .apexcharts-canvas {
    & .apexcharts-tooltip {
      background-color: white !important;
      color: var(--color-gray-700) !important;
      border: 0 !important;
      border-radius: 0.25rem !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    .dark & .apexcharts-tooltip {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
      border-color: transparent !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    & .apexcharts-tooltip .apexcharts-tooltip-title {
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;
      margin-bottom: 0.75rem !important;
      background-color: var(--color-gray-100) !important;
      border-bottom-color: var(--color-gray-200) !important;
      font-size: 0.875rem !important;
      font-weight: 400 !important;
      color: var(--color-gray-500) !important;
    }
    .dark & .apexcharts-tooltip .apexcharts-tooltip-title {
      background-color: var(--color-gray-600) !important;
      border-color: var(--color-gray-500) !important;
      color: var(--color-gray-500) !important;
    }
    & .apexcharts-xaxistooltip {
      color: var(--color-gray-500) !important;
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;
      border-color: transparent !important;
      background-color: white !important;
      border-radius: 0.25rem !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    .dark & .apexcharts-xaxistooltip {
      color: var(--color-gray-400) !important;
      background-color: var(--color-gray-700) !important;
    }
    & .apexcharts-tooltip .apexcharts-tooltip-text-y-label {
      color: var(--color-gray-500) !important;
      font-size: 0.875rem !important;
    }
    .dark & .apexcharts-tooltip .apexcharts-tooltip-text-y-label {
      color: var(--color-gray-400) !important;
    }
    & .apexcharts-tooltip .apexcharts-tooltip-text-y-value {
      color: var(--color-gray-900);
      font-size: 0.875rem !important;
    }
    .dark & .apexcharts-tooltip .apexcharts-tooltip-text-y-value {
      color: white !important;
    }
    & .apexcharts-xaxistooltip-text {
      font-weight: 400 !important;
      font-size: 0.875rem !important;
    }
    & .apexcharts-xaxistooltip:after {
      border-bottom-color: white !important;
    }
    & .apexcharts-xaxistooltip:before {
      border-bottom-color: white !important;
    }
    & .apexcharts-xaxistooltip:after {
      border-width: 8px !important;
      margin-left: -8px !important;
    }
    & .apexcharts-xaxistooltip:before {
      border-width: 10px !important;
      margin-left: -10px !important;
    }
    .dark & .apexcharts-xaxistooltip:after {
      border-bottom-color: var(--color-gray-700) !important;
    }
    .dark & .apexcharts-xaxistooltip:before {
      border-bottom-color: var(--color-gray-700) !important;
    }
    & .apexcharts-tooltip-series-group.apexcharts-active .apexcharts-tooltip-y-group {
      padding: 0 !important;
    }
    & .apexcharts-tooltip-series-group.apexcharts-active {
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
      padding-bottom: 0.75rem !important;
      background-color: white !important;
      color: var(--color-gray-500) !important;
    }
    .dark & .apexcharts-tooltip-series-group.apexcharts-active {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
    }
    & .apexcharts-tooltip-series-group.apexcharts-active:first-of-type {
      padding-top: 0.75rem !important;
    }
    & .apexcharts-legend {
      padding: 0 !important;
    }
    & .apexcharts-legend-text {
      font-size: 0.75rem !important;
      font-weight: 500 !important;
      padding-left: 1.25rem !important;
      color: var(--color-gray-500) !important;
    }
    :is([dir=rtl]) & .apexcharts-legend-text {
      padding-right: 0.5rem !important;
    }
    & .apexcharts-legend-text:not(.apexcharts-inactive-legend):hover {
      color: var(--color-gray-900) !important;
    }
    .dark & .apexcharts-legend-text {
      color: var(--color-gray-400) !important;
    }
    .dark & .apexcharts-legend-text:not(.apexcharts-inactive-legend):hover {
      color: white !important;
    }
    & .apexcharts-legend-series {
      margin-left: 0.5rem !important;
      margin-right: 0.5rem !important;
      margin-bottom: 0.25rem !important;
      display: flex !important;
      align-items: center !important;
    }
    .dark & .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value {
      fill: white !important;
    }
    & .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
      fill: var(--color-gray-500) !important;
      font-size: 1rem,[object Object] !important;
      font-weight: 400 !important;
    }
    .dark & .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
      fill: var(--color-gray-400) !important;
    }
    & .apexcharts-datalabels .apexcharts-text.apexcharts-pie-label {
      font-size: 0.75rem,[object Object] !important;
      font-weight: 600 !important;
      text-shadow: none !important;
      filter: none !important;
    }
  }
  .apexcharts-legend-series {
    .apexcharts-canvas & {
      margin-left: 0.5rem !important;
      margin-right: 0.5rem !important;
      margin-bottom: 0.25rem !important;
      display: flex !important;
      align-items: center !important;
    }
  }
  .apexcharts-tooltip {
    .apexcharts-canvas & {
      background-color: white !important;
      color: var(--color-gray-700) !important;
      border: 0 !important;
      border-radius: 0.25rem !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    .dark .apexcharts-canvas & {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
      border-color: transparent !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    .apexcharts-canvas & .apexcharts-tooltip-title {
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;
      margin-bottom: 0.75rem !important;
      background-color: var(--color-gray-100) !important;
      border-bottom-color: var(--color-gray-200) !important;
      font-size: 0.875rem !important;
      font-weight: 400 !important;
      color: var(--color-gray-500) !important;
    }
    .dark .apexcharts-canvas & .apexcharts-tooltip-title {
      background-color: var(--color-gray-600) !important;
      border-color: var(--color-gray-500) !important;
      color: var(--color-gray-500) !important;
    }
    .apexcharts-canvas & .apexcharts-tooltip-text-y-label {
      color: var(--color-gray-500) !important;
      font-size: 0.875rem !important;
    }
    .dark .apexcharts-canvas & .apexcharts-tooltip-text-y-label {
      color: var(--color-gray-400) !important;
    }
    .apexcharts-canvas & .apexcharts-tooltip-text-y-value {
      color: var(--color-gray-900);
      font-size: 0.875rem !important;
    }
    :is([dir=rtl]) & .apexcharts-tooltip-marker {
      margin-right: 0px !important;
      margin-left: e !important;
    }
    .dark .apexcharts-canvas & .apexcharts-tooltip-text-y-value {
      color: white !important;
    }
  }
  .datatable-top {
    .datatable-wrapper & {
      display: flex;
      justify-content: space-between;
      flex-direction: column-reverse;
      align-items: start;
      gap: 1rem;
      margin-bottom: 1rem;
      @media (min-width: 640px) {
        flex-direction: row-reverse;
        align-items: center;
      }
    }
    .datatable-wrapper & .datatable-dropdown {
      color: var(--color-gray-500);
      font-size: 0.875rem;
    }
    .dark .datatable-wrapper & .datatable-dropdown {
      color: var(--color-gray-400);
    }
    .datatable-wrapper & .datatable-dropdown .datatable-selector {
      background-color: var(--color-gray-50);
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      margin-right: 0.25rem;
      min-width: 4rem;
    }
    .dark .datatable-wrapper & .datatable-dropdown .datatable-selector {
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
      color: white;
    }
  }
  .apexcharts-tooltip-marker {
    :is([dir=rtl]) .apexcharts-tooltip & {
      margin-right: 0px !important;
      margin-left: e !important;
    }
  }
  .datatable-dropdown {
    .datatable-wrapper .datatable-top & {
      color: var(--color-gray-500);
      font-size: 0.875rem;
    }
    .dark .datatable-wrapper .datatable-top & {
      color: var(--color-gray-400);
    }
    .datatable-wrapper .datatable-top & .datatable-selector {
      background-color: var(--color-gray-50);
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      margin-right: 0.25rem;
      min-width: 4rem;
    }
    .dark .datatable-wrapper .datatable-top & .datatable-selector {
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
      color: white;
    }
  }
  .datatable-selector {
    .datatable-wrapper .datatable-top .datatable-dropdown & {
      background-color: var(--color-gray-50);
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      margin-right: 0.25rem;
      min-width: 4rem;
    }
    .dark .datatable-wrapper .datatable-top .datatable-dropdown & {
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
      color: white;
    }
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-2\.5 {
    margin-right: calc(var(--spacing) * 2.5);
  }
  .apexcharts-tooltip-title {
    .apexcharts-canvas .apexcharts-tooltip & {
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;
      margin-bottom: 0.75rem !important;
      background-color: var(--color-gray-100) !important;
      border-bottom-color: var(--color-gray-200) !important;
      font-size: 0.875rem !important;
      font-weight: 400 !important;
      color: var(--color-gray-500) !important;
    }
    .dark .apexcharts-canvas .apexcharts-tooltip & {
      background-color: var(--color-gray-600) !important;
      border-color: var(--color-gray-500) !important;
      color: var(--color-gray-500) !important;
    }
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-2\.5 {
    margin-bottom: calc(var(--spacing) * 2.5);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .apexcharts-xaxistooltip {
    .apexcharts-canvas & {
      color: var(--color-gray-500) !important;
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;
      border-color: transparent !important;
      background-color: white !important;
      border-radius: 0.25rem !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    .dark .apexcharts-canvas & {
      color: var(--color-gray-400) !important;
      background-color: var(--color-gray-700) !important;
    }
    .apexcharts-canvas &:after {
      border-bottom-color: white !important;
    }
    .apexcharts-canvas &:before {
      border-bottom-color: white !important;
    }
    .apexcharts-canvas &:after {
      border-width: 8px !important;
      margin-left: -8px !important;
    }
    .apexcharts-canvas &:before {
      border-width: 10px !important;
      margin-left: -10px !important;
    }
    .dark .apexcharts-canvas &:after {
      border-bottom-color: var(--color-gray-700) !important;
    }
    .dark .apexcharts-canvas &:before {
      border-bottom-color: var(--color-gray-700) !important;
    }
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-auto {
    margin-left: auto;
  }
  .datatable-pagination-list {
    .datatable-wrapper .datatable-bottom .datatable-pagination & {
      display: flex;
      align-items: center;
      height: 2rem;
      font-size: 0.875rem;
    }
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-\[calc\(100\%-1rem\)\] {
    height: calc(100% - 1rem);
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-full {
    max-height: 100%;
  }
  .datatable-table {
    .datatable-wrapper & {
      width: 100%;
      font-size: 0.875rem;
      color: var(--color-gray-500);
      text-align: left;
    }
    .dark .datatable-wrapper & {
      color: var(--color-gray-400);
    }
    .datatable-wrapper & thead {
      font-size: 0.75rem;
      color: var(--color-gray-500);
      background-color: var(--color-gray-50);
    }
    .dark .datatable-wrapper & thead {
      color: var(--color-gray-400);
      background-color: var(--color-gray-800);
    }
    .datatable-wrapper & thead th {
      white-space: nowrap;
    }
    .datatable-wrapper & thead th {
      width: auto !important;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    .datatable-wrapper & tbody th {
      width: auto !important;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    .datatable-wrapper & tbody td {
      width: auto !important;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    .datatable-wrapper & thead th .datatable-sorter {
      text-transform: uppercase;
    }
    .datatable-wrapper & thead th {
      text-transform: uppercase;
    }
    .datatable-wrapper & thead th .datatable-sorter:hover {
      color: var(--color-gray-900);
    }
    .datatable-wrapper & thead th.datatable-ascending .datatable-sorter {
      color: var(--color-gray-900);
    }
    .datatable-wrapper & thead th.datatable-descending .datatable-sorter {
      color: var(--color-gray-900);
    }
    .dark .datatable-wrapper & thead th .datatable-sorter:hover {
      color: white;
    }
    .dark .datatable-wrapper & thead th.datatable-ascending .datatable-sorter {
      color: white;
    }
    .dark .datatable-wrapper & thead th.datatable-descending .datatable-sorter {
      color: white;
    }
    .datatable-wrapper & tbody tr.selected {
      background-color: var(--color-gray-100);
    }
    .dark .datatable-wrapper & tbody tr.selected {
      background-color: var(--color-gray-700);
    }
    .datatable-wrapper & tbody tr {
      border-bottom: 1px solid var(--color-gray-200);
    }
    .dark .datatable-wrapper & tbody tr {
      border-bottom: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper & .datatable-empty {
      text-align: center;
    }
  }
  .w-1\/2 {
    width: calc(1/2 * 100%);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-80 {
    width: calc(var(--spacing) * 80);
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-6xl {
    max-width: var(--container-6xl);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .datatable-input {
    .datatable-wrapper .datatable-search & {
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      background-color: var(--color-gray-50);
      min-width: 16rem;
    }
    .datatable-wrapper & {
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      background-color: var(--color-gray-50);
      min-width: 16rem;
    }
    .dark .datatable-wrapper .datatable-search & {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    .dark .datatable-wrapper & {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper thead th & {
      background-color: white;
      font-weight: 400;
      color: var(--color-gray-900);
      padding-top: .35rem;
      padding-bottom: .35rem;
      min-width: 0;
    }
    .dark .datatable-wrapper thead th & {
      background-color: var(--color-gray-700);
      border-color: var(--color-gray-600);
      color: white;
    }
    .datatable-wrapper .datatable-search &:focus {
      border-color: var(--color-blue-600);
    }
  }
  .datatable-search {
    .datatable-wrapper & .datatable-input {
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      background-color: var(--color-gray-50);
      min-width: 16rem;
    }
    .dark .datatable-wrapper & .datatable-input {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper & .datatable-input:focus {
      border-color: var(--color-blue-600);
    }
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink {
    flex-shrink: 1;
  }
  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-full {
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-full {
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-full {
    --tw-translate-y: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .rotate-180 {
    rotate: 180deg;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .transform-none {
    transform: none;
  }
  .cursor-default {
    cursor: default;
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .columns-2 {
    columns: 2;
  }
  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .datatable-container {
    .datatable-wrapper & thead tr.search-filtering-row th {
      padding-top: 0;
    }
    .datatable-wrapper & {
      overflow-x: auto;
    }
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-x-hidden {
    overflow-x: hidden;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-t {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }
  .rounded-l-lg {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }
  .rounded-r-lg {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }
  .rounded-b {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .dark {
    & .apexcharts-canvas .apexcharts-tooltip {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
      border-color: transparent !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    & .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-title {
      background-color: var(--color-gray-600) !important;
      border-color: var(--color-gray-500) !important;
      color: var(--color-gray-500) !important;
    }
    & .apexcharts-canvas .apexcharts-xaxistooltip {
      color: var(--color-gray-400) !important;
      background-color: var(--color-gray-700) !important;
    }
    & .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-text-y-label {
      color: var(--color-gray-400) !important;
    }
    & .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-text-y-value {
      color: white !important;
    }
    & .apexcharts-canvas .apexcharts-xaxistooltip:after {
      border-bottom-color: var(--color-gray-700) !important;
    }
    & .apexcharts-canvas .apexcharts-xaxistooltip:before {
      border-bottom-color: var(--color-gray-700) !important;
    }
    & .apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
    }
    & .apexcharts-canvas .apexcharts-legend-text {
      color: var(--color-gray-400) !important;
    }
    & .apexcharts-canvas .apexcharts-legend-text:not(.apexcharts-inactive-legend):hover {
      color: white !important;
    }
    & .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value {
      fill: white !important;
    }
    & .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
      fill: var(--color-gray-400) !important;
    }
    & .apexcharts-gridline {
      stroke: var(--color-gray-700) !important;
    }
    & .apexcharts-xcrosshairs {
      stroke: var(--color-gray-700) !important;
    }
    & .apexcharts-ycrosshairs {
      stroke: var(--color-gray-700) !important;
    }
  }
  .dark {
    & .datatable-wrapper .datatable-search .datatable-input {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    & .datatable-wrapper .datatable-input {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    & .datatable-wrapper thead th .datatable-input {
      background-color: var(--color-gray-700);
      border-color: var(--color-gray-600);
      color: white;
    }
    & .datatable-wrapper .datatable-top .datatable-dropdown {
      color: var(--color-gray-400);
    }
    & .datatable-wrapper .datatable-top .datatable-dropdown .datatable-selector {
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
      color: white;
    }
    & .datatable-wrapper .datatable-table {
      color: var(--color-gray-400);
    }
    & .datatable-wrapper .datatable-table thead {
      color: var(--color-gray-400);
      background-color: var(--color-gray-800);
    }
    & .datatable-wrapper .datatable-table thead th .datatable-sorter:hover {
      color: white;
    }
    & .datatable-wrapper .datatable-table thead th.datatable-ascending .datatable-sorter {
      color: white;
    }
    & .datatable-wrapper .datatable-table thead th.datatable-descending .datatable-sorter {
      color: white;
    }
    & .datatable-wrapper .datatable-table tbody tr.selected {
      background-color: var(--color-gray-700);
    }
    & .datatable-wrapper .datatable-table tbody tr {
      border-bottom: 1px solid var(--color-gray-700);
    }
    & .datatable-wrapper .datatable-bottom .datatable-info {
      color: var(--color-gray-400);
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link {
      color: var(--color-gray-400);
      border-color: var(--color-gray-700);
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%239CA3AF' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m14 8-4 4 4 4'/%3e %3c/svg%3e");
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='%239CA3AF' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' width='20' height='20' fill='none' viewBox='0 0 24 24'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m10 16 4-4-4-4'/%3e %3c/svg%3e");
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-left: 1px solid var(--color-gray-700);
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-700);
      color: white;
    }
  }
  .border-\[var\(--border-color\)\] {
    border-color: var(--border-color);
  }
  .border-blue-600 {
    border-color: var(--color-blue-600);
  }
  .border-blue-700 {
    border-color: var(--color-blue-700);
  }
  .border-gray-100 {
    border-color: var(--color-gray-100);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .apexcharts-active {
    .apexcharts-canvas .apexcharts-tooltip-series-group& .apexcharts-tooltip-y-group {
      padding: 0 !important;
    }
    .apexcharts-canvas .apexcharts-tooltip-series-group& {
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
      padding-bottom: 0.75rem !important;
      background-color: white !important;
      color: var(--color-gray-500) !important;
    }
    .dark .apexcharts-canvas .apexcharts-tooltip-series-group& {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
    }
    .apexcharts-canvas .apexcharts-tooltip-series-group&:first-of-type {
      padding-top: 0.75rem !important;
    }
  }
  .selected {
    .datatable-wrapper .datatable-table tbody tr& {
      background-color: var(--color-gray-100);
    }
    .dark .datatable-wrapper .datatable-table tbody tr& {
      background-color: var(--color-gray-700);
    }
  }
  .selectedCell {
    background-color: var(--color-gray-50);
    .dark & {
      background-color: var(--color-gray-700);
    }
  }
  .bg-\[var\(--background-primary\)\] {
    background-color: var(--background-primary);
  }
  .bg-\[var\(--background-secondary\)\] {
    background-color: var(--background-secondary);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-700 {
    background-color: var(--color-blue-700);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }
  .bg-gray-900\/50 {
    background-color: color-mix(in srgb, #111827 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-gray-900) 50%, transparent);
    }
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\/50 {
    background-color: color-mix(in srgb, #ffffff 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
    }
  }
  .dark {
    & .selectedCell {
      background-color: var(--color-gray-700);
    }
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-red-500 {
    --tw-gradient-from: var(--color-red-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-10\% {
    --tw-gradient-from-position: 10%;
  }
  .to-red-700 {
    --tw-gradient-to: var(--color-red-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-60\% {
    --tw-gradient-to-position: 60%;
  }
  .bg-clip-text {
    background-clip: text;
  }
  .apexcharts-datalabels-group {
    & .apexcharts-text.apexcharts-datalabel-value {
      fill: var(--color-gray-900) !important;
      font-size: 1.875rem,[object Object] !important;
      font-weight: 700 !important;
    }
    .dark .apexcharts-canvas & .apexcharts-text.apexcharts-datalabel-value {
      fill: white !important;
    }
    .apexcharts-canvas & .apexcharts-text.apexcharts-datalabel-label {
      fill: var(--color-gray-500) !important;
      font-size: 1rem,[object Object] !important;
      font-weight: 400 !important;
    }
    .dark .apexcharts-canvas & .apexcharts-text.apexcharts-datalabel-label {
      fill: var(--color-gray-400) !important;
    }
  }
  .apexcharts-datalabel-label {
    .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text& {
      fill: var(--color-gray-500) !important;
      font-size: 1rem,[object Object] !important;
      font-weight: 400 !important;
    }
    .dark .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text& {
      fill: var(--color-gray-400) !important;
    }
  }
  .apexcharts-datalabel-value {
    .apexcharts-datalabels-group .apexcharts-text& {
      fill: var(--color-gray-900) !important;
      font-size: 1.875rem,[object Object] !important;
      font-weight: 700 !important;
    }
    .dark .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text& {
      fill: white !important;
    }
  }
  .apexcharts-ycrosshairs {
    stroke: var(--color-gray-200) !important;
    .dark & {
      stroke: var(--color-gray-700) !important;
    }
  }
  .apexcharts-legend {
    .apexcharts-canvas & {
      padding: 0 !important;
    }
  }
  .apexcharts-tooltip-y-group {
    .apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active & {
      padding: 0 !important;
    }
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }
  .p-2\.5 {
    padding: calc(var(--spacing) * 2.5);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .ps-3 {
    padding-inline-start: calc(var(--spacing) * 3);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .search-filtering-row {
    .datatable-wrapper .datatable-container thead tr& th {
      padding-top: 0;
    }
  }
  .apexcharts-legend-text {
    .apexcharts-canvas & {
      font-size: 0.75rem !important;
      font-weight: 500 !important;
      padding-left: 1.25rem !important;
      color: var(--color-gray-500) !important;
    }
    :is([dir=rtl]) .apexcharts-canvas & {
      padding-right: 0.5rem !important;
    }
    .apexcharts-canvas &:not(.apexcharts-inactive-legend):hover {
      color: var(--color-gray-900) !important;
    }
    .dark .apexcharts-canvas & {
      color: var(--color-gray-400) !important;
    }
    .dark .apexcharts-canvas &:not(.apexcharts-inactive-legend):hover {
      color: white !important;
    }
  }
  .datatable-empty {
    .datatable-wrapper .datatable-table & {
      text-align: center;
    }
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .apexcharts-datalabels {
    .apexcharts-canvas & .apexcharts-text.apexcharts-pie-label {
      font-size: 0.75rem,[object Object] !important;
      font-weight: 600 !important;
      text-shadow: none !important;
      filter: none !important;
    }
  }
  .apexcharts-pie-label {
    .apexcharts-canvas .apexcharts-datalabels .apexcharts-text& {
      font-size: 0.75rem,[object Object] !important;
      font-weight: 600 !important;
      text-shadow: none !important;
      filter: none !important;
    }
  }
  .apexcharts-xaxistooltip-text {
    .apexcharts-canvas & {
      font-weight: 400 !important;
      font-size: 0.875rem !important;
    }
  }
  .apexcharts-tooltip-text-y-label {
    .apexcharts-canvas .apexcharts-tooltip & {
      color: var(--color-gray-500) !important;
      font-size: 0.875rem !important;
    }
    .dark .apexcharts-canvas .apexcharts-tooltip & {
      color: var(--color-gray-400) !important;
    }
  }
  .apexcharts-tooltip-text-y-value {
    .apexcharts-canvas .apexcharts-tooltip & {
      color: var(--color-gray-900);
      font-size: 0.875rem !important;
    }
    .dark .apexcharts-canvas .apexcharts-tooltip & {
      color: white !important;
    }
  }
  .datatable-info {
    .datatable-wrapper .datatable-bottom & {
      color: var(--color-gray-500);
      font-size: 0.875rem;
    }
    .dark .datatable-wrapper .datatable-bottom & {
      color: var(--color-gray-400);
    }
  }
  .leading-6 {
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }
  .leading-9 {
    --tw-leading: calc(var(--spacing) * 9);
    line-height: calc(var(--spacing) * 9);
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .datatable-sorter {
    .datatable-wrapper .datatable-table thead th & {
      text-transform: uppercase;
    }
    .datatable-wrapper .datatable-table thead th &:hover {
      color: var(--color-gray-900);
    }
    .datatable-wrapper .datatable-table thead th.datatable-ascending & {
      color: var(--color-gray-900);
    }
    .datatable-wrapper .datatable-table thead th.datatable-descending & {
      color: var(--color-gray-900);
    }
    .dark .datatable-wrapper .datatable-table thead th &:hover {
      color: white;
    }
    .dark .datatable-wrapper .datatable-table thead th.datatable-ascending & {
      color: white;
    }
    .dark .datatable-wrapper .datatable-table thead th.datatable-descending & {
      color: white;
    }
  }
  .datatable-ascending {
    .datatable-wrapper .datatable-table thead th& .datatable-sorter {
      color: var(--color-gray-900);
    }
    .dark .datatable-wrapper .datatable-table thead th& .datatable-sorter {
      color: white;
    }
  }
  .datatable-descending {
    .datatable-wrapper .datatable-table thead th& .datatable-sorter {
      color: var(--color-gray-900);
    }
    .dark .datatable-wrapper .datatable-table thead th& .datatable-sorter {
      color: white;
    }
  }
  .text-\[var\(--text-primary\)\] {
    color: var(--text-primary);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-green-700 {
    color: var(--color-green-700);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-green-900 {
    color: var(--color-green-900);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-red-900 {
    color: var(--color-red-900);
  }
  .text-transparent {
    color: transparent;
  }
  .text-white {
    color: var(--color-white);
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-500 {
    --tw-duration: 500ms;
    transition-duration: 500ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .hover\:border-gray-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-300);
      }
    }
  }
  .hover\:bg-\[var\(--background-secondary\)\] {
    &:hover {
      @media (hover: hover) {
        background-color: var(--background-secondary);
      }
    }
  }
  .hover\:bg-blue-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-800);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:bg-gray-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-600);
      }
    }
  }
  .hover\:bg-white {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-white);
      }
    }
  }
  .hover\:text-blue-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
  }
  .hover\:text-gray-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-600);
      }
    }
  }
  .hover\:text-gray-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .focus\:z-10 {
    &:focus {
      z-index: 10;
    }
  }
  .focus\:border-red-500 {
    &:focus {
      border-color: var(--color-red-500);
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-4 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-blue-300 {
    &:focus {
      --tw-ring-color: var(--color-blue-300);
    }
  }
  .focus\:ring-gray-200 {
    &:focus {
      --tw-ring-color: var(--color-gray-200);
    }
  }
  .focus\:ring-red-500 {
    &:focus {
      --tw-ring-color: var(--color-red-500);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .sm\:rounded-lg {
    @media (width >= 40rem) {
      border-radius: var(--radius-lg);
    }
  }
  .md\:inset-0 {
    @media (width >= 48rem) {
      inset: calc(var(--spacing) * 0);
    }
  }
  .rtl\:rotate-180 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      rotate: 180deg;
    }
  }
  .rtl\:space-x-reverse {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 1;
      }
    }
  }
  .dark\:border-blue-500 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      border-color: var(--color-blue-500);
    }
  }
  .dark\:border-gray-500 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      border-color: var(--color-gray-500);
    }
  }
  .dark\:border-gray-600 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      border-color: var(--color-gray-600);
    }
  }
  .dark\:border-gray-700 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      border-color: var(--color-gray-700);
    }
  }
  .dark\:border-transparent {
    &:where([data-theme=dark], [data-theme=dark] *) {
      border-color: transparent;
    }
  }
  .dark\:bg-blue-600 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      background-color: var(--color-blue-600);
    }
  }
  .dark\:bg-blue-900 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      background-color: var(--color-blue-900);
    }
  }
  .dark\:bg-gray-600 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      background-color: var(--color-gray-600);
    }
  }
  .dark\:bg-gray-700 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      background-color: var(--color-gray-700);
    }
  }
  .dark\:bg-gray-800 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      background-color: var(--color-gray-800);
    }
  }
  .dark\:bg-gray-800\/50 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      background-color: color-mix(in srgb, #1F2937 50%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);
      }
    }
  }
  .dark\:bg-gray-900\/80 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      background-color: color-mix(in srgb, #111827 80%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-gray-900) 80%, transparent);
      }
    }
  }
  .dark\:bg-green-900 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      background-color: var(--color-green-900);
    }
  }
  .dark\:bg-red-900 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      background-color: var(--color-red-900);
    }
  }
  .dark\:text-blue-200 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      color: var(--color-blue-200);
    }
  }
  .dark\:text-blue-500 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      color: var(--color-blue-500);
    }
  }
  .dark\:text-gray-300 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      color: var(--color-gray-300);
    }
  }
  .dark\:text-gray-400 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      color: var(--color-gray-400);
    }
  }
  .dark\:text-gray-500 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      color: var(--color-gray-500);
    }
  }
  .dark\:text-green-200 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      color: var(--color-green-200);
    }
  }
  .dark\:text-green-400 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      color: var(--color-green-400);
    }
  }
  .dark\:text-red-200 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      color: var(--color-red-200);
    }
  }
  .dark\:text-red-400 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      color: var(--color-red-400);
    }
  }
  .dark\:text-white {
    &:where([data-theme=dark], [data-theme=dark] *) {
      color: var(--color-white);
    }
  }
  .dark\:hover\:bg-blue-700 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-blue-700);
        }
      }
    }
  }
  .dark\:hover\:bg-gray-600 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-gray-600);
        }
      }
    }
  }
  .dark\:hover\:bg-gray-800 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-gray-800);
        }
      }
    }
  }
  .dark\:hover\:text-blue-500 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-blue-500);
        }
      }
    }
  }
  .dark\:hover\:text-gray-300 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-gray-300);
        }
      }
    }
  }
  .dark\:hover\:text-white {
    &:where([data-theme=dark], [data-theme=dark] *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-white);
        }
      }
    }
  }
  .dark\:focus\:ring-blue-800 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      &:focus {
        --tw-ring-color: var(--color-blue-800);
      }
    }
  }
  .dark\:focus\:ring-gray-600 {
    &:where([data-theme=dark], [data-theme=dark] *) {
      &:focus {
        --tw-ring-color: var(--color-gray-600);
      }
    }
  }
}
#navContainer {
  display: none;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  background-color: var(--background-secondary);
  color: var(--text-secondary);
  @media (width >= 48rem) {
    display: grid;
  }
  @media (width >= 48rem) {
    max-height: 10%;
  }
  @media (width >= 64rem) {
    max-height: 8%;
  }
  #navBrand {
    margin: calc(var(--spacing) * 3);
    #logo {
      display: inline-block;
      &:hover {
        @media (hover: hover) {
          --tw-brightness: brightness(120%);
          filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
        }
      }
      @media (width >= 48rem) {
        max-width: 10%;
      }
      @media (width >= 64rem) {
        max-width: 8%;
      }
    }
  }
  #desktopMenu {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
    display: none;
    place-self: center;
    @media (width >= 48rem) {
      display: block;
    }
    ul {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: calc(var(--spacing) * 2.5);
      padding: calc(var(--spacing) * 2);
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
      color: var(--text-secondary);
      li a {
        border-radius: var(--radius-lg);
        padding-inline: calc(var(--spacing) * 4);
        padding-block: calc(var(--spacing) * 2);
        transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
        --tw-duration: 300ms;
        transition-duration: 300ms;
        --tw-ease: var(--ease-in-out);
        transition-timing-function: var(--ease-in-out);
        &:hover {
          @media (hover: hover) {
            color: var(--text-primary);
          }
        }
      }
    }
  }
}
#m-NavContainer {
  @media (width >= 48rem) {
    display: none;
  }
  nav {
    position: fixed;
    right: calc(var(--spacing) * 0);
    bottom: calc(var(--spacing) * 0);
    left: calc(var(--spacing) * 0);
    z-index: 50;
    @media (width >= 48rem) {
      display: none;
    }
  }
  .menu-grid {
    display: grid;
    height: 100%;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
    border-color: var(--border-color);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--border-color) 20%, transparent);
    }
    background-color: var(--background-secondary);
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .nav-item {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-inline: calc(var(--spacing) * 5);
    padding-block: calc(var(--spacing) * 3);
    color: var(--text-secondary);
    i {
      height: calc(var(--spacing) * 6);
      width: calc(var(--spacing) * 6);
    }
    &:active {
      --tw-scale-x: 90%;
      --tw-scale-y: 90%;
      --tw-scale-z: 90%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .drawer {
    position: fixed;
    top: calc(var(--spacing) * 0);
    z-index: 50;
    height: 100vh;
    width: calc(var(--spacing) * 80);
    overflow-y: auto;
    padding: calc(var(--spacing) * 4);
    &[data-drawer-placement="left"] {
      left: calc(var(--spacing) * 0);
      --tw-translate-x: -100%;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    &[data-drawer-placement="right"] {
      right: calc(var(--spacing) * 0);
      --tw-translate-x: 100%;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    background-color: var(--background-primary);
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .drawer-header {
    margin-bottom: calc(var(--spacing) * 4);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    border-color: var(--border-color);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--border-color) 20%, transparent);
    }
    padding-bottom: calc(var(--spacing) * 4);
    h1 {
      --tw-gradient-position: to right in oklab;
      background-image: linear-gradient(var(--tw-gradient-stops));
      --tw-gradient-from: var(--color-red-500);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      --tw-gradient-from-position: 10%;
      --tw-gradient-to: var(--color-red-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      --tw-gradient-to-position: 60%;
      background-clip: text;
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
      --tw-font-weight: var(--font-weight-bold);
      font-weight: var(--font-weight-bold);
      color: transparent;
    }
    .drawer-close {
      border-radius: var(--radius-lg);
      padding: calc(var(--spacing) * 1.5);
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
      color: var(--text-secondary);
    }
  }
}
.drawer-content {
  padding-block: calc(var(--spacing) * 4);
  color: var(--text-secondary);
  & h5 {
    margin-top: calc(var(--spacing) * 2.5);
    margin-bottom: calc(var(--spacing) * 2.5);
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  ul {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    a {
      display: flex;
      align-items: center;
      padding: calc(var(--spacing) * 2.5);
      i {
        margin-inline-end: calc(var(--spacing) * 3);
        height: calc(var(--spacing) * 5);
        width: calc(var(--spacing) * 5);
      }
    }
  }
}
#social-drawer {
  #social-icons {
    display: flex;
    justify-content: center;
    gap: calc(var(--spacing) * 5);
    a {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
      transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
    }
  }
}
#config-drawer {
  #theme-container {
    margin-bottom: calc(var(--spacing) * 6);
    button {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
      @media (width >= 48rem) {
        font-size: var(--text-3xl);
        line-height: var(--tw-leading, var(--text-3xl--line-height));
      }
    }
  }
  #language-container {
    margin-top: calc(var(--spacing) * 12);
    margin-bottom: calc(var(--spacing) * 12);
    #language {
      & .langItem {
        display: grid;
        align-items: center;
        justify-content: flex-start;
        & .fi {
          cursor: pointer;
          font-size: var(--text-3xl);
          line-height: var(--tw-leading, var(--text-3xl--line-height));
          --tw-saturate: saturate(0%);
          filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
          transition-property: all;
          transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
          transition-duration: var(--tw-duration, var(--default-transition-duration));
          --tw-duration: 400ms;
          transition-duration: 400ms;
          --tw-ease: var(--ease-in-out);
          transition-timing-function: var(--ease-in-out);
          &:hover {
            @media (hover: hover) {
              --tw-saturate: saturate(100%);
              filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
            }
          }
          @media (width >= 48rem) {
            font-size: var(--text-2xl);
            line-height: var(--tw-leading, var(--text-2xl--line-height));
          }
          &.active {
            font-size: var(--text-4xl);
            line-height: var(--tw-leading, var(--text-4xl--line-height));
            --tw-font-weight: var(--font-weight-bold);
            font-weight: var(--font-weight-bold);
            color: var(--color-red-600);
            --tw-saturate: saturate(100%);
            filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
            @media (width >= 48rem) {
              font-size: var(--text-3xl);
              line-height: var(--tw-leading, var(--text-3xl--line-height));
            }
          }
          & p {
            position: fixed;
            left: calc(5/4 * 100%);
            width: calc(var(--spacing) * 20);
            font-size: var(--text-sm);
            line-height: var(--tw-leading, var(--text-sm--line-height));
            @media (width >= 48rem) {
              font-size: var(--text-base);
              line-height: var(--tw-leading, var(--text-base--line-height));
            }
          }
        }
      }
    }
  }
}
#social-drawer {
  #social-icons {
    display: flex;
    gap: calc(var(--spacing) * 5);
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
    @media (width >= 48rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
    & .fa-x-twitter {
      color: var(--color-sky-600);
    }
    & .fa-soundcloud {
      color: var(--color-orange-600);
    }
    & .fa-youtube {
      color: var(--color-red-600);
    }
    & :hover {
      &.fa-x-twitter {
        color: var(--color-sky-500);
      }
      &.fa-soundcloud {
        color: var(--color-orange-500);
      }
      &.fa-youtube {
        color: var(--color-red-700);
      }
    }
  }
}
main {
  min-height: 100vh;
  background-color: var(--background-primary);
  padding-inline: calc(var(--spacing) * 2.5);
  padding-top: calc(var(--spacing) * 10);
  padding-bottom: calc(var(--spacing) * 10);
  color: var(--text-primary);
  --tw-outline-style: none;
  outline-style: none;
  h1 {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
    --tw-gradient-from: var(--color-red-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    --tw-gradient-from-position: 10%;
    --tw-gradient-to: var(--color-red-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    --tw-gradient-to-position: 60%;
    background-clip: text;
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    color: transparent;
  }
  form {
    margin-block: calc(var(--spacing) * 5);
  }
  .hBtn {
    cursor: pointer;
    border-radius: 0.25rem;
    background-color: var(--color-red-600);
    padding-inline: calc(var(--spacing) * 5);
    padding-block: calc(var(--spacing) * 2.5);
    text-align: center;
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
    color: var(--color-white);
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 500ms;
    transition-duration: 500ms;
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
    &:hover {
      @media (hover: hover) {
        --tw-saturate: saturate(80%);
        filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
      }
    }
  }
  .hero-buttons, .social-buttons {
    margin-top: calc(var(--spacing) * 10);
    display: flex;
    flex-direction: column;
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
    @media (width >= 40rem) {
      flex-direction: row;
    }
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .skill-card, .stats-card {
    & .fas {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
      color: var(--color-red-600);
    }
  }
}
#home {
  align-items: center;
  justify-content: center;
  .hero-section {
    margin-inline: auto;
    animation: fade-in 1s ease-in-out;
    overflow: hidden;
    padding-block: calc(var(--spacing) * 16);
    @media (width >= 48rem) {
      max-width: 80%;
    }
    .hero-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      @media (width >= 64rem) {
        flex-direction: row;
      }
    }
    .hero-content {
      margin-bottom: calc(var(--spacing) * 2.5);
      p {
        margin-top: calc(var(--spacing) * 4);
        text-align: start;
        font-size: var(--text-lg);
        line-height: var(--tw-leading, var(--text-lg--line-height));
      }
      .hero-buttons {
        margin-top: calc(var(--spacing) * 10);
        display: flex;
        flex-direction: column;
        :where(& > :not(:last-child)) {
          --tw-space-y-reverse: 0;
          margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
          margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
        }
        @media (width >= 40rem) {
          flex-direction: row;
        }
        @media (width >= 40rem) {
          :where(& > :not(:last-child)) {
            --tw-space-y-reverse: 0;
            margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
            margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
          }
        }
        @media (width >= 40rem) {
          :where(& > :not(:last-child)) {
            --tw-space-x-reverse: 0;
            margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
            margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
          }
        }
        i {
          margin-right: calc(var(--spacing) * 2);
        }
      }
    }
    .floating-image {
      margin-inline: auto;
      margin-top: calc(var(--spacing) * 30);
      width: 100%;
      animation: float 6s ease-in-out infinite;
      @media (width >= 48rem) {
        width: calc(1/2 * 100%);
      }
    }
  }
}
#highlight-section {
  margin-inline: auto;
  margin-block: calc(var(--spacing) * 15);
  @media (width >= 48rem) {
    max-width: 80%;
  }
  @media (width >= 64rem) {
    max-width: 60%;
  }
  #featured-card {
    border-radius: var(--radius-3xl);
    background-color: var(--background-secondary);
    padding: calc(var(--spacing) * 6);
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    #card-header {
      margin-bottom: calc(var(--spacing) * 4);
      text-align: center;
      h2 {
        font-size: var(--text-3xl);
        line-height: var(--tw-leading, var(--text-3xl--line-height));
        --tw-font-weight: var(--font-weight-bold);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
      }
      p {
        font-size: var(--text-lg);
        line-height: var(--tw-leading, var(--text-lg--line-height));
        color: var(--text-secondary);
      }
    }
    #highlightContainer {
      margin-bottom: calc(var(--spacing) * 4);
      iframe {
        aspect-ratio: var(--aspect-video);
        width: 100%;
        border-radius: var(--radius-lg);
      }
      .highlight-wrapper {
        width: 100%;
        border-radius: var(--radius-lg);
        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        .video-container {
          position: relative;
          aspect-ratio: var(--aspect-video);
          border-top-left-radius: var(--radius-lg);
          border-top-right-radius: var(--radius-lg);
          iframe {
            position: absolute;
            top: calc(var(--spacing) * 0);
            left: calc(var(--spacing) * 0);
            height: 100%;
            width: 100%;
            border-top-left-radius: var(--radius-lg);
            border-top-right-radius: var(--radius-lg);
          }
          .placeholder-content {
            position: absolute;
            top: calc(var(--spacing) * 0);
            left: calc(var(--spacing) * 0);
            display: flex;
            height: 100%;
            width: 100%;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: color-mix(in srgb, #000000 80%, transparent);
            @supports (color: color-mix(in lab, red, red)) {
              background-color: color-mix(in oklab, var(--color-black) 80%, transparent);
            }
            padding: calc(var(--spacing) * 5);
            text-align: center;
            color: var(--color-white);
            .placeholder-icon {
              margin-bottom: calc(var(--spacing) * 4);
              font-size: var(--text-5xl);
              line-height: var(--tw-leading, var(--text-5xl--line-height));
              color: var(--color-red-600);
            }
            p {
              margin-bottom: calc(var(--spacing) * 6);
              font-size: var(--text-lg);
              line-height: var(--tw-leading, var(--text-lg--line-height));
            }
            .load-video-btn {
              display: flex;
              transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
              align-items: center;
              border-radius: calc(infinity * 1px);
              background-color: var(--color-red-600);
              padding-inline: calc(var(--spacing) * 6);
              padding-block: calc(var(--spacing) * 3);
              color: var(--color-white);
              transition-property: all;
              transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
              transition-duration: var(--tw-duration, var(--default-transition-duration));
              --tw-duration: 300ms;
              transition-duration: 300ms;
              &:hover {
                @media (hover: hover) {
                  --tw-scale-x: 105%;
                  --tw-scale-y: 105%;
                  --tw-scale-z: 105%;
                  scale: var(--tw-scale-x) var(--tw-scale-y);
                }
              }
              &:hover {
                @media (hover: hover) {
                  background-color: var(--color-red-700);
                }
              }
              i {
                margin-right: calc(var(--spacing) * 2);
              }
            }
          }
        }
      }
      .highlight-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: calc(var(--spacing) * 10);
        text-align: center;
        min-height: 250px;
        .loading-spinner {
          margin-bottom: calc(var(--spacing) * 4);
          font-size: var(--text-4xl);
          line-height: var(--tw-leading, var(--text-4xl--line-height));
          color: var(--color-red-600);
        }
        p {
          color: var(--text-secondary);
        }
      }
      .highlight-error {
        display: flex;
        min-height: 250px;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: calc(var(--spacing) * 10);
        text-align: center;
        .error-icon {
          margin-bottom: calc(var(--spacing) * 4);
          font-size: var(--text-4xl);
          line-height: var(--tw-leading, var(--text-4xl--line-height));
          color: var(--color-red-600);
        }
        p {
          margin-bottom: calc(var(--spacing) * 6);
          color: var(--text-secondary);
        }
        .retry-btn {
          display: flex;
          align-items: center;
          border-radius: 0.25rem;
          background-color: var(--color-red-600);
          padding-inline: calc(var(--spacing) * 4);
          padding-block: calc(var(--spacing) * 2);
          color: var(--color-white);
          transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
          transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
          transition-duration: var(--tw-duration, var(--default-transition-duration));
          &:hover {
            @media (hover: hover) {
              background-color: var(--color-red-700);
            }
          }
          i {
            margin-right: calc(var(--spacing) * 2);
          }
        }
      }
    }
    .button-container {
      text-align: center;
      button {
        display: inline-flex;
        width: 100%;
        align-items: center;
        justify-content: center;
        border-radius: var(--radius-lg);
        background-color: var(--color-red-700);
        padding-inline: calc(var(--spacing) * 5);
        padding-block: calc(var(--spacing) * 2.5);
        text-align: center;
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
        --tw-font-weight: var(--font-weight-medium);
        font-weight: var(--font-weight-medium);
        color: var(--color-white);
        &:hover {
          @media (hover: hover) {
            background-color: var(--color-red-800);
          }
        }
        &:focus {
          --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
          box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
        &:focus {
          --tw-ring-color: var(--color-red-300);
        }
        &:focus {
          --tw-outline-style: none;
          outline-style: none;
        }
        &:where([data-theme=dark], [data-theme=dark] *) {
          background-color: var(--color-red-600);
        }
        &:where([data-theme=dark], [data-theme=dark] *) {
          &:hover {
            @media (hover: hover) {
              background-color: var(--color-red-700);
            }
          }
        }
        &:where([data-theme=dark], [data-theme=dark] *) {
          &:focus {
            --tw-ring-color: var(--color-red-800);
          }
        }
        i {
          margin-right: calc(var(--spacing) * 2);
        }
        p {
          font-size: var(--text-lg);
          line-height: var(--tw-leading, var(--text-lg--line-height));
          --tw-font-weight: var(--font-weight-medium);
          font-weight: var(--font-weight-medium);
        }
      }
    }
  }
}
#social-proof {
  padding-bottom: calc(var(--spacing) * 5);
  #statsContainer {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: calc(var(--spacing) * 2.5);
    .stats-card {
      margin-block: auto;
      width: 100%;
      border-radius: var(--radius-3xl);
      background-color: var(--background-secondary);
      padding: calc(var(--spacing) * 8);
      text-align: center;
      color: var(--text-primary);
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      --tw-duration: 300ms;
      transition-duration: 300ms;
      --tw-ease: var(--ease-in-out);
      transition-timing-function: var(--ease-in-out);
      @media (width >= 48rem) {
        max-width: calc(var(--spacing) * 50);
      }
      @media (width >= 64rem) {
        max-width: calc(var(--spacing) * 70);
      }
      i {
        font-size: var(--text-5xl);
        line-height: var(--tw-leading, var(--text-5xl--line-height));
        color: var(--color-red-600);
      }
      .counter {
        margin-block: calc(var(--spacing) * 4);
        font-size: var(--text-4xl);
        line-height: var(--tw-leading, var(--text-4xl--line-height));
        --tw-font-weight: var(--font-weight-bold);
        font-weight: var(--font-weight-bold);
      }
    }
  }
}
@keyframes fade-in {
  from {
    opacity: 0.5;
  }
  to {
    opacity: 1;
  }
}
#streaming {
  .streaming-header {
    animation: fade-in 1s ease-in-out;
    text-align: center;
    h1 {
      margin-bottom: calc(var(--spacing) * 4);
    }
    p {
      margin-bottom: calc(var(--spacing) * 5);
    }
  }
  .streaming-content {
    margin-bottom: calc(var(--spacing) * 5);
    min-height: 250px;
    .streaming-controls {
      margin-bottom: calc(var(--spacing) * 6);
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: var(--radius-lg);
      background-color: var(--background-secondary);
      padding: calc(var(--spacing) * 3);
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      .tabs-container {
        display: flex;
        gap: calc(var(--spacing) * 2);
        .tab-button {
          display: flex;
          align-items: center;
          gap: calc(var(--spacing) * 2);
          border-radius: var(--radius-lg);
          padding-inline: calc(var(--spacing) * 4);
          padding-block: calc(var(--spacing) * 2);
          transition-property: all;
          transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
          transition-duration: var(--tw-duration, var(--default-transition-duration));
          --tw-duration: 300ms;
          transition-duration: 300ms;
          &.active {
            background-color: var(--accent-color);
            color: var(--color-white);
            &:hover {
              @media (hover: hover) {
                background-color: var(--accent-color-hover);
              }
            }
          }
          &:not(.active) {
            &:hover {
              @media (hover: hover) {
                background-color: var(--background-hover);
              }
            }
          }
          i {
            font-size: var(--text-lg);
            line-height: var(--tw-leading, var(--text-lg--line-height));
          }
        }
      }
      .view-controls {
        .view-toggle {
          display: flex;
          height: calc(var(--spacing) * 10);
          width: calc(var(--spacing) * 10);
          cursor: pointer;
          align-items: center;
          justify-content: center;
          border-radius: calc(infinity * 1px);
          transition-property: all;
          transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
          transition-duration: var(--tw-duration, var(--default-transition-duration));
          --tw-duration: 300ms;
          transition-duration: 300ms;
          &:hover {
            @media (hover: hover) {
              background-color: var(--background-hover);
            }
          }
          i {
            font-size: var(--text-lg);
            line-height: var(--tw-leading, var(--text-lg--line-height));
          }
        }
      }
    }
    .search-section {
      margin-bottom: calc(var(--spacing) * 6);
    }
    .youtube-section {
      min-height: 250px;
      animation: fade-in 0.5s ease-in-out;
    }
    .spotify-section {
      min-height: 250px;
      animation: fade-in 0.5s ease-in-out;
    }
    #youtubeSearch, #spotifySearch {
      margin-bottom: calc(var(--spacing) * 4);
      display: block;
      width: 100%;
      padding: calc(var(--spacing) * 4);
      padding-inline-start: calc(var(--spacing) * 10);
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
    #youtubeSearch {
      border-radius: var(--radius-lg);
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-red-500);
      background-color: var(--is-red-20);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--is-red-20) 85%, transparent);
      }
      color: var(--is-red-70);
      &::placeholder {
        color: var(--color-red-500);
      }
      &:focus {
        border-color: var(--color-red-600);
      }
      &:focus {
        &::placeholder {
          color: var(--color-red-600);
        }
      }
      &:focus {
        --tw-ring-color: var(--color-red-600);
      }
    }
    #spotifySearch {
      border-radius: var(--radius-lg);
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-green-400);
      background-color: color-mix(in srgb, #064E3B 85%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-green-900) 85%, transparent);
      }
      color: var(--color-green-300);
      &::placeholder {
        color: var(--color-green-600);
      }
      &:focus {
        border-color: var(--color-green-400);
      }
      &:focus {
        &::placeholder {
          color: var(--color-green-400);
        }
      }
      &:focus {
        --tw-ring-color: var(--color-green-400);
      }
    }
    .streaming-container {
      display: grid;
      grid-template-columns: repeat(1, minmax(0, 1fr));
      gap: calc(var(--spacing) * 4);
      @media (width >= 48rem) {
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }
      @media (width >= 64rem) {
        grid-template-columns: repeat(3, minmax(0, 1fr));
      }
      .playlist-item {
        overflow: hidden;
        border-radius: var(--radius-lg);
        background-color: var(--background-secondary);
        text-align: center;
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        transition-property: all;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
        --tw-duration: 300ms;
        transition-duration: 300ms;
        &.is-favorite {
          --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
          box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
          --tw-ring-color: var(--color-yellow-400);
        }
        .video-container {
          position: relative;
          aspect-ratio: var(--aspect-video);
          iframe {
            position: absolute;
            top: calc(var(--spacing) * 0);
            left: calc(var(--spacing) * 0);
            height: 100%;
            width: 100%;
          }
          .placeholder {
            position: absolute;
            top: calc(var(--spacing) * 0);
            left: calc(var(--spacing) * 0);
            display: flex;
            height: 100%;
            width: 100%;
            align-items: center;
            justify-content: center;
            background-color: color-mix(in srgb, #000000 50%, transparent);
            @supports (color: color-mix(in lab, red, red)) {
              background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
            }
            .placeholder-content {
              padding: calc(var(--spacing) * 4);
              text-align: center;
              &.spotify {
                background-color: color-mix(in srgb, #064E3B 30%, transparent);
                @supports (color: color-mix(in lab, red, red)) {
                  background-color: color-mix(in oklab, var(--color-green-900) 30%, transparent);
                }
                .placeholder-icon {
                  color: var(--color-green-400);
                }
                .load-video-btn {
                  background-color: var(--color-green-600);
                  &:hover {
                    @media (hover: hover) {
                      background-color: var(--color-green-700);
                    }
                  }
                }
              }
              .placeholder-icon {
                margin-bottom: calc(var(--spacing) * 3);
                font-size: var(--text-5xl);
                line-height: var(--tw-leading, var(--text-5xl--line-height));
                color: var(--color-red-500);
              }
              p {
                margin-bottom: calc(var(--spacing) * 4);
                --tw-font-weight: var(--font-weight-medium);
                font-weight: var(--font-weight-medium);
                color: var(--color-white);
              }
              .load-video-btn {
                cursor: pointer;
                border-radius: var(--radius-lg);
                background-color: var(--color-red-600);
                padding-inline: calc(var(--spacing) * 4);
                padding-block: calc(var(--spacing) * 2);
                color: var(--color-white);
                transition-property: all;
                transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
                transition-duration: var(--tw-duration, var(--default-transition-duration));
                --tw-duration: 300ms;
                transition-duration: 300ms;
                &:hover {
                  @media (hover: hover) {
                    background-color: var(--color-red-700);
                  }
                }
                i {
                  margin-right: calc(var(--spacing) * 2);
                }
              }
            }
          }
        }
        .playlist-info {
          padding: calc(var(--spacing) * 4);
          .playlist-header {
            margin-bottom: calc(var(--spacing) * 2);
            display: flex;
            align-items: center;
            justify-content: space-between;
            h3 {
              font-size: var(--text-lg);
              line-height: var(--tw-leading, var(--text-lg--line-height));
              --tw-font-weight: var(--font-weight-bold);
              font-weight: var(--font-weight-bold);
            }
            .favorite-button {
              display: flex;
              height: calc(var(--spacing) * 8);
              width: calc(var(--spacing) * 8);
              cursor: pointer;
              align-items: center;
              justify-content: center;
              border-radius: calc(infinity * 1px);
              transition-property: all;
              transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
              transition-duration: var(--tw-duration, var(--default-transition-duration));
              --tw-duration: 300ms;
              transition-duration: 300ms;
              &:hover {
                @media (hover: hover) {
                  background-color: var(--background-hover);
                }
              }
              i {
                color: var(--color-stone-400);
              }
              &.active i {
                color: var(--color-yellow-400);
              }
            }
          }
          .playlist-actions {
            margin-top: calc(var(--spacing) * 3);
            display: flex;
            justify-content: center;
            gap: calc(var(--spacing) * 2);
            .action-button {
              border-radius: 0.25rem;
              padding-inline: calc(var(--spacing) * 3);
              padding-block: calc(var(--spacing) * 1.5);
              font-size: var(--text-sm);
              line-height: var(--tw-leading, var(--text-sm--line-height));
              color: var(--color-white);
              transition-property: all;
              transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
              transition-duration: var(--tw-duration, var(--default-transition-duration));
              &.youtube {
                background-color: var(--color-red-600);
                &:hover {
                  @media (hover: hover) {
                    background-color: var(--color-red-700);
                  }
                }
              }
              &.spotify {
                background-color: var(--color-green-600);
                &:hover {
                  @media (hover: hover) {
                    background-color: var(--color-green-700);
                  }
                }
              }
            }
          }
        }
      }
      .no-results {
        grid-column: 1 / -1;
        padding-block: calc(var(--spacing) * 10);
        text-align: center;
        color: var(--text-secondary);
      }
      .error-message {
        grid-column: 1 / -1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-block: calc(var(--spacing) * 10);
        text-align: center;
        i {
          margin-bottom: calc(var(--spacing) * 4);
          font-size: var(--text-4xl);
          line-height: var(--tw-leading, var(--text-4xl--line-height));
          color: var(--color-red-500);
        }
        p {
          margin-bottom: calc(var(--spacing) * 4);
          color: var(--text-secondary);
        }
        .retry-button {
          border-radius: var(--radius-lg);
          background-color: var(--accent-color);
          padding-inline: calc(var(--spacing) * 4);
          padding-block: calc(var(--spacing) * 2);
          color: var(--color-white);
          transition-property: all;
          transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
          transition-duration: var(--tw-duration, var(--default-transition-duration));
          --tw-duration: 300ms;
          transition-duration: 300ms;
          &:hover {
            @media (hover: hover) {
              background-color: var(--accent-color-hover);
            }
          }
          i {
            margin-right: calc(var(--spacing) * 2);
            margin-bottom: calc(var(--spacing) * 0);
            font-size: var(--text-base);
            line-height: var(--tw-leading, var(--text-base--line-height));
            color: var(--color-white);
          }
        }
      }
    }
    .pagination-container {
      margin-top: calc(var(--spacing) * 6);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: calc(var(--spacing) * 1);
      .pagination-button {
        display: flex;
        height: calc(var(--spacing) * 10);
        width: calc(var(--spacing) * 10);
        align-items: center;
        justify-content: center;
        border-radius: var(--radius-lg);
        color: var(--color-red-600);
        transition-property: all;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
        --tw-duration: 300ms;
        transition-duration: 300ms;
        cursor: pointer;
        &:hover {
          @media (hover: hover) {
            --tw-brightness: brightness(0.9);
            filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
          }
        }
        &.active {
          color: var(--text-primary);
        }
      }
      .pagination-ellipsis {
        display: flex;
        height: calc(var(--spacing) * 10);
        width: calc(var(--spacing) * 10);
        align-items: center;
        justify-content: center;
      }
    }
    #loadingAnimation {
      position: fixed;
      top: calc(1/2 * 100%);
      left: calc(1/2 * 100%);
      z-index: 50;
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
      --tw-translate-y: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
      transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
      .spinner {
        display: inline-block;
        height: calc(var(--spacing) * 12);
        width: calc(var(--spacing) * 12);
        animation: var(--animate-spin);
        border-radius: calc(infinity * 1px);
        border-style: var(--tw-border-style);
        border-width: 4px;
        border-top-color: var(--color-red-500);
        border-color: var(--is-red-50) transparent transparent transparent;
      }
    }
  }
  .spotify-playlist, .youtube-playlist {
    margin-bottom: calc(var(--spacing) * 7.5);
    overflow: hidden;
    border-radius: var(--radius-lg);
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    iframe {
      aspect-ratio: var(--aspect-video);
      width: 100%;
      border-radius: var(--radius-lg);
    }
  }
  .playlist-links {
    margin-bottom: calc(var(--spacing) * 7.5);
    display: grid;
    max-width: 100%;
    .btn {
      border-radius: var(--radius-lg);
      padding-inline: calc(var(--spacing) * 4);
      padding-block: calc(var(--spacing) * 2);
      transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      --tw-duration: 300ms;
      transition-duration: 300ms;
      i {
        margin-right: calc(var(--spacing) * 2);
      }
    }
    .youtube {
      background-color: var(--color-red-500);
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-red-700);
        }
      }
    }
    .spotify {
      background-color: var(--color-green-500);
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-green-700);
        }
      }
    }
  }
}
body.view-grid {
  #streaming {
    .streaming-container {
      grid-template-columns: repeat(1, minmax(0, 1fr));
      @media (width >= 48rem) {
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }
      @media (width >= 64rem) {
        grid-template-columns: repeat(3, minmax(0, 1fr));
      }
      .playlist-item {
        flex-direction: column;
        .video-container {
          width: 100%;
        }
        .playlist-info {
          width: 100%;
          text-align: center;
          .playlist-actions {
            justify-content: center;
          }
        }
      }
    }
  }
}
body.view-list {
  #streaming {
    .streaming-container {
      grid-template-columns: repeat(1, minmax(0, 1fr));
      .playlist-item {
        display: flex;
        flex-direction: column;
        @media (width >= 48rem) {
          flex-direction: row;
        }
        .video-container {
          @media (width >= 48rem) {
            width: calc(1/3 * 100%);
          }
        }
        .playlist-info {
          text-align: left;
          @media (width >= 48rem) {
            width: calc(2/3 * 100%);
          }
          .playlist-header {
            justify-content: space-between;
          }
          .playlist-actions {
            justify-content: flex-start;
          }
        }
      }
    }
  }
}
#about {
  .about-section {
    margin-bottom: calc(var(--spacing) * 15);
    display: flex;
    animation: fade-in 1s ease-in-out;
    flex-direction: row;
    justify-content: center;
    .aboutHeader {
      margin-inline: auto;
      display: flex;
      flex-direction: column;
      @media (width >= 48rem) {
        max-width: 80%;
      }
      .floating-image {
        margin-top: calc(var(--spacing) * 7.5);
        display: flex;
        animation: float 6s ease-in-out infinite;
        justify-content: flex-end;
        @media (width >= 64rem) {
          margin-top: calc(var(--spacing) * 0);
        }
        img {
          width: 100%;
          @media (width >= 48rem) {
            width: calc(1/2 * 100%);
          }
        }
      }
      p {
        margin-top: calc(var(--spacing) * 4);
        text-align: start;
        font-size: var(--text-lg);
        line-height: var(--tw-leading, var(--text-lg--line-height));
        text-wrap: wrap;
      }
    }
  }
  .skills-section {
    margin-bottom: calc(var(--spacing) * 15);
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: calc(var(--spacing) * 4);
    .skill-card {
      width: 100%;
      border-radius: var(--radius-lg);
      background-color: var(--background-secondary);
      padding-inline: calc(var(--spacing) * 2.5);
      padding-block: calc(var(--spacing) * 5);
      text-align: center;
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      transition-property: all;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      --tw-duration: 500ms;
      transition-duration: 500ms;
      --tw-ease: var(--ease-in-out);
      transition-timing-function: var(--ease-in-out);
      @media (width >= 48rem) {
        max-width: calc(var(--spacing) * 50);
      }
      @media (width >= 64rem) {
        max-width: calc(var(--spacing) * 70);
      }
      h3 {
        margin-bottom: calc(var(--spacing) * 2.5);
        font-size: var(--text-2xl);
        line-height: var(--tw-leading, var(--text-2xl--line-height));
        --tw-font-weight: var(--font-weight-semibold);
        font-weight: var(--font-weight-semibold);
      }
      .skill-bar {
        height: calc(var(--spacing) * 2);
        overflow: hidden;
        border-radius: calc(infinity * 1px);
        background-color: var(--background-primary);
        .progress {
          height: 100%;
          animation: slideRight 1s ease-in-out;
          border-radius: calc(infinity * 1px);
          background-color: var(--accent-color);
        }
      }
    }
  }
  .connect-section {
    margin-bottom: calc(var(--spacing) * 15);
    display: grid;
    justify-items: center;
    .connect-card {
      display: flex;
      width: 100%;
      flex-direction: column;
      align-items: center;
      border-radius: var(--radius-lg);
      background-color: var(--background-secondary);
      padding-inline: calc(var(--spacing) * 2.5);
      padding-block: calc(var(--spacing) * 5);
      text-align: center;
      transition-property: all;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      --tw-duration: 500ms;
      transition-duration: 500ms;
      --tw-ease: var(--ease-in-out);
      transition-timing-function: var(--ease-in-out);
      @media (width >= 48rem) {
        max-width: calc(4/5 * 100%);
      }
    }
  }
}
#TERMS {
  .tos-section {
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 8);
    .tos-container {
      margin-inline: auto;
      max-width: var(--breakpoint-xl);
      .header-content {
        margin-bottom: calc(var(--spacing) * 8);
        text-align: center;
        .description {
          margin-top: calc(var(--spacing) * 3);
          font-size: var(--text-xl);
          line-height: var(--tw-leading, var(--text-xl--line-height));
          color: var(--text-primary);
        }
        .date {
          margin-top: calc(var(--spacing) * 2);
          color: var(--is-red-60);
        }
      }
      .cards-grid {
        display: grid;
        gap: calc(var(--spacing) * 8);
        @media (width >= 48rem) {
          grid-template-columns: repeat(2, minmax(0, 1fr));
        }
        @media (width >= 64rem) {
          grid-template-columns: repeat(3, minmax(0, 1fr));
        }
        .tos-card {
          border-radius: var(--radius-lg);
          background-color: var(--background-secondary);
          padding: calc(var(--spacing) * 6);
          --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
          box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
          .card-header {
            margin-bottom: calc(var(--spacing) * 4);
            display: flex;
            align-items: center;
            i {
              margin-right: calc(var(--spacing) * 3);
              font-size: var(--text-2xl);
              line-height: var(--tw-leading, var(--text-2xl--line-height));
              color: var(--text-secondary);
            }
            h2 {
              font-size: var(--text-xl);
              line-height: var(--tw-leading, var(--text-xl--line-height));
              --tw-font-weight: var(--font-weight-bold);
              font-weight: var(--font-weight-bold);
              color: var(--text-primary);
            }
          }
          p {
            color: var(--text-secondary);
          }
        }
      }
    }
  }
}
#PRIVACY {
  .privacy-section {
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 8);
    .privacy-container {
      margin-inline: auto;
      max-width: var(--breakpoint-xl);
      .header-content {
        margin-bottom: calc(var(--spacing) * 8);
        text-align: center;
        .description {
          margin-top: calc(var(--spacing) * 3);
          font-size: var(--text-xl);
          line-height: var(--tw-leading, var(--text-xl--line-height));
          color: var(--text-primary);
        }
        .date {
          margin-top: calc(var(--spacing) * 2);
          color: var(--is-red-60);
        }
      }
      .cards-grid {
        display: grid;
        gap: calc(var(--spacing) * 8);
        @media (width >= 48rem) {
          grid-template-columns: repeat(2, minmax(0, 1fr));
        }
        @media (width >= 64rem) {
          grid-template-columns: repeat(3, minmax(0, 1fr));
        }
        .privacy-card {
          border-radius: var(--radius-lg);
          background-color: var(--background-secondary);
          padding: calc(var(--spacing) * 6);
          --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
          box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
          .card-header {
            margin-bottom: calc(var(--spacing) * 4);
            display: flex;
            align-items: center;
            i {
              margin-right: calc(var(--spacing) * 3);
              font-size: var(--text-2xl);
              line-height: var(--tw-leading, var(--text-2xl--line-height));
              color: var(--text-secondary);
            }
            h2 {
              font-size: var(--text-xl);
              line-height: var(--tw-leading, var(--text-xl--line-height));
              --tw-font-weight: var(--font-weight-bold);
              font-weight: var(--font-weight-bold);
              color: var(--text-primary);
            }
          }
          p {
            color: var(--text-secondary);
          }
        }
      }
    }
  }
}
.updates-section {
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 8);
  .updates-container {
    margin-inline: auto;
    max-width: var(--breakpoint-xl);
    .header-content {
      margin-bottom: calc(var(--spacing) * 8);
      text-align: center;
      .description {
        margin-top: calc(var(--spacing) * 3);
        font-size: var(--text-xl);
        line-height: var(--tw-leading, var(--text-xl--line-height));
        color: var(--text-primary);
      }
    }
    .updates-grid {
      display: grid;
      gap: calc(var(--spacing) * 8);
      @media (width >= 48rem) {
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }
      @media (width >= 64rem) {
        grid-template-columns: repeat(3, minmax(0, 1fr));
      }
      .update-card {
        border-radius: var(--radius-lg);
        background-color: var(--background-secondary);
        padding: calc(var(--spacing) * 6);
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        .card-header {
          margin-bottom: calc(var(--spacing) * 4);
          display: flex;
          align-items: center;
          i {
            margin-right: calc(var(--spacing) * 3);
            font-size: var(--text-2xl);
            line-height: var(--tw-leading, var(--text-2xl--line-height));
            color: var(--text-secondary);
          }
          h2 {
            font-size: var(--text-xl);
            line-height: var(--tw-leading, var(--text-xl--line-height));
            --tw-font-weight: var(--font-weight-bold);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
          }
        }
        .version-badge {
          display: inline-flex;
          align-items: center;
          border-radius: calc(infinity * 1px);
          padding-inline: calc(var(--spacing) * 2.5);
          padding-block: calc(var(--spacing) * 0.5);
          font-size: var(--text-xs);
          line-height: var(--tw-leading, var(--text-xs--line-height));
          --tw-font-weight: var(--font-weight-medium);
          font-weight: var(--font-weight-medium);
          background-color: var(--color-red-100);
          color: var(--color-red-800);
          &:where([data-theme=dark], [data-theme=dark] *) {
            background-color: var(--color-red-900);
          }
          &:where([data-theme=dark], [data-theme=dark] *) {
            color: var(--color-red-200);
          }
          margin-top: calc(var(--spacing) * 2);
        }
        .update-content {
          margin-top: calc(var(--spacing) * 4);
          color: var(--text-primary);
          .update-actions {
            margin-top: calc(var(--spacing) * 4);
            a {
              &:hover {
                @media (hover: hover) {
                  --tw-font-weight: var(--font-weight-bold);
                  font-weight: var(--font-weight-bold);
                }
              }
              &:hover {
                @media (hover: hover) {
                  color: var(--color-red-600);
                }
              }
            }
          }
          update-text {
            margin-bottom: calc(var(--spacing) * 4);
          }
          ul, ol {
            margin-bottom: calc(var(--spacing) * 4);
            padding-left: calc(var(--spacing) * 5);
          }
          li {
            margin-bottom: calc(var(--spacing) * 1);
          }
          a {
            color: var(--is-red-60);
            &:hover {
              @media (hover: hover) {
                text-decoration-line: underline;
              }
            }
          }
        }
      }
    }
  }
}
.error-container {
  display: flex;
  min-height: 100vh;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  h1 {
    margin-bottom: calc(var(--spacing) * 4);
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }
  h2 {
    margin-bottom: calc(var(--spacing) * 4);
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
  }
  p {
    margin-inline: auto;
    margin-bottom: calc(var(--spacing) * 6);
    max-width: var(--container-lg);
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
    color: var(--text-secondary);
  }
  .btn {
    display: inline-flex;
    cursor: pointer;
    align-items: center;
    border-radius: var(--radius-lg);
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
    --tw-gradient-from: var(--color-red-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    --tw-gradient-from-position: 10%;
    --tw-gradient-to: var(--color-red-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    --tw-gradient-to-position: 60%;
    padding-inline: calc(var(--spacing) * 5);
    padding-block: calc(var(--spacing) * 2.5);
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    color: var(--color-white);
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 500ms;
    transition-duration: 500ms;
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
    &:hover {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
      transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    i {
      margin-right: calc(var(--spacing) * 2);
    }
  }
  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
    100% {
      transform: translateY(0px);
    }
  }
  .gradient-text {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
    --tw-gradient-from: var(--color-red-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    --tw-gradient-from-position: 10%;
    --tw-gradient-to: var(--color-red-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    --tw-gradient-to-position: 60%;
    background-clip: text;
    color: transparent;
    animation: float 6s ease-in-out infinite;
  }
}
footer {
  background-color: var(--background-secondary);
  padding: calc(var(--spacing) * 3);
  color: var(--text-primary);
  h4 {
    margin-top: calc(var(--spacing) * 3);
    margin-bottom: calc(var(--spacing) * 3);
    cursor: default;
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    &:hover {
      @media (hover: hover) {
        color: var(--accent-color);
      }
    }
  }
  #desktopFooter {
    display: none;
    @media (width >= 48rem) {
      display: block;
    }
    #footer-content, #footer-bottom {
      margin: calc(var(--spacing) * 1);
      display: grid;
      grid-template-columns: repeat(1, minmax(0, 1fr));
      gap: calc(var(--spacing) * 1);
      @media (width >= 48rem) {
        margin: calc(var(--spacing) * 5);
      }
      @media (width >= 48rem) {
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }
      #quick-links {
        .f-link {
          cursor: pointer;
          &:hover {
            @media (hover: hover) {
              --tw-font-weight: var(--font-weight-bold);
              font-weight: var(--font-weight-bold);
            }
          }
          &:hover {
            @media (hover: hover) {
              color: var(--color-red-600);
            }
          }
        }
      }
      #social-links {
        #social-icons {
          display: flex;
          gap: calc(var(--spacing) * 5);
          font-size: var(--text-4xl);
          line-height: var(--tw-leading, var(--text-4xl--line-height));
          @media (width >= 48rem) {
            font-size: var(--text-3xl);
            line-height: var(--tw-leading, var(--text-3xl--line-height));
          }
          & .fa-x-twitter {
            color: var(--color-sky-600);
          }
          & .fa-soundcloud {
            color: var(--color-orange-600);
          }
          & .fa-youtube {
            color: var(--color-red-600);
          }
          & :hover {
            &.fa-x-twitter {
              color: var(--color-sky-500);
            }
            &.fa-soundcloud {
              color: var(--color-orange-500);
            }
            &.fa-youtube {
              color: var(--color-red-700);
            }
          }
        }
      }
      #theme-container div button {
        font-size: var(--text-4xl);
        line-height: var(--tw-leading, var(--text-4xl--line-height));
        @media (width >= 48rem) {
          font-size: var(--text-3xl);
          line-height: var(--tw-leading, var(--text-3xl--line-height));
        }
      }
      #language-container {
        padding-bottom: calc(var(--spacing) * 2);
        #language {
          & .langItem {
            display: grid;
            align-items: center;
            justify-content: flex-start;
            & .fi {
              cursor: pointer;
              font-size: var(--text-3xl);
              line-height: var(--tw-leading, var(--text-3xl--line-height));
              --tw-saturate: saturate(0%);
              filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
              transition-property: all;
              transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
              transition-duration: var(--tw-duration, var(--default-transition-duration));
              --tw-duration: 400ms;
              transition-duration: 400ms;
              --tw-ease: var(--ease-in-out);
              transition-timing-function: var(--ease-in-out);
              &:hover {
                @media (hover: hover) {
                  --tw-saturate: saturate(100%);
                  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
                }
              }
              @media (width >= 48rem) {
                font-size: var(--text-2xl);
                line-height: var(--tw-leading, var(--text-2xl--line-height));
              }
              &.active {
                font-size: var(--text-4xl);
                line-height: var(--tw-leading, var(--text-4xl--line-height));
                --tw-saturate: saturate(100%);
                filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
                @media (width >= 48rem) {
                  font-size: var(--text-3xl);
                  line-height: var(--tw-leading, var(--text-3xl--line-height));
                }
              }
            }
            & p {
              position: fixed;
              bottom: calc(var(--spacing) * 1);
              left: calc(5/4 * 100%);
              width: calc(var(--spacing) * 20);
              font-size: var(--text-sm);
              line-height: var(--tw-leading, var(--text-sm--line-height));
              @media (width >= 48rem) {
                font-size: var(--text-base);
                line-height: var(--tw-leading, var(--text-base--line-height));
              }
              @media (width >= 64rem) {
                font-size: var(--text-lg);
                line-height: var(--tw-leading, var(--text-lg--line-height));
              }
            }
          }
        }
      }
    }
    #copyright {
      #f-divider {
        margin-block: calc(var(--spacing) * 2.5);
        opacity: 25%;
      }
      p {
        margin-bottom: calc(var(--spacing) * 2.5);
        --tw-font-weight: var(--font-weight-light);
        font-weight: var(--font-weight-light);
        color: var(--text-secondary);
      }
    }
  }
}
[data-theme-selector] {
  display: flex;
  align-items: center;
  gap: calc(var(--spacing) * 5);
}
.theme-button, [data-theme-switch] {
  position: relative;
  display: flex;
  --tw-scale-x: 80%;
  --tw-scale-y: 80%;
  --tw-scale-z: 80%;
  scale: var(--tw-scale-x) var(--tw-scale-y);
  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  cursor: pointer;
  align-items: center;
  justify-content: center;
  transition-property: all;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
  --tw-duration: 300ms;
  transition-duration: 300ms;
  --tw-ease: var(--ease-in-out);
  transition-timing-function: var(--ease-in-out);
}
.theme-button.active, [data-theme-switch].active {
  --tw-scale-x: 100%;
  --tw-scale-y: 100%;
  --tw-scale-z: 100%;
  scale: var(--tw-scale-x) var(--tw-scale-y);
  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
}
.theme-button:hover, [data-theme-switch]:hover {
  opacity: 0.9;
}
[data-theme-value="light"] {
  color: var(--color-yellow-500);
}
[data-theme-value="dark"] {
  color: var(--color-blue-500);
}
[data-theme-value="black"] {
  color: var(--color-white);
}
[data-theme="light"] [data-theme-value="black"] {
  color: var(--color-neutral-400);
}
[data-theme-value="red"] {
  color: var(--color-red-600);
}
.header [data-theme-switch], .header .theme-button {
  @media (width >= 48rem) {
    height: calc(var(--spacing) * 10);
  }
  @media (width >= 48rem) {
    width: calc(var(--spacing) * 10);
  }
  @media (width >= 48rem) {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
}
@keyframes theme-transition {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
.theme-transition {
  animation: theme-transition 0.3s ease;
}
[data-theme-switch]::after {
  content: attr(title);
  position: absolute;
  bottom: calc(var(--spacing) * 0);
  left: calc(1/2 * 100%);
  display: none;
  --tw-translate-x: calc(calc(1/2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  background-color: var(--color-black);
  padding-inline: calc(var(--spacing) * 2);
  padding-block: calc(var(--spacing) * 1);
  font-size: var(--text-xs);
  line-height: var(--tw-leading, var(--text-xs--line-height));
  color: var(--color-white);
  opacity: 0%;
  transition-property: all;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
  --tw-duration: 200ms;
  transition-duration: 200ms;
  --tw-ease: var(--ease-in-out);
  transition-timing-function: var(--ease-in-out);
}
[data-theme-switch]:hover::after {
  visibility: visible;
  opacity: 100%;
}
.update-notification {
  position: fixed;
  right: calc(var(--spacing) * 0);
  bottom: calc(var(--spacing) * 0);
  left: calc(var(--spacing) * 0);
  z-index: 50;
  --tw-translate-y: 100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  background-color: var(--color-red-600);
  color: var(--color-white);
  --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  transition-property: transform, translate, scale, rotate;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
  --tw-duration: 300ms;
  transition-duration: 300ms;
  --tw-ease: var(--ease-in-out);
  transition-timing-function: var(--ease-in-out);
  &.show {
    transform: none;
  }
  .update-content {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
    margin-inline: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: calc(var(--spacing) * 4);
    @media (width >= 48rem) {
      flex-direction: row;
    }
    max-width: 768px;
  }
  .update-icon {
    margin-right: calc(var(--spacing) * 4);
    display: none;
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
    @media (width >= 48rem) {
      display: block;
    }
    i {
      animation: spin-slow;
    }
  }
  .update-text {
    flex-grow: 1;
    p {
      margin-bottom: calc(var(--spacing) * 0);
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
      --tw-font-weight: var(--font-weight-bold);
      font-weight: var(--font-weight-bold);
    }
    small {
      color: color-mix(in srgb, #ffffff 80%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-white) 80%, transparent);
      }
    }
  }
  .update-actions {
    margin-top: calc(var(--spacing) * 3);
    display: flex;
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .update-btn {
    border-radius: 0.25rem;
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 200ms;
    transition-duration: 200ms;
    &.primary {
      margin-left: calc(var(--spacing) * 2);
      background-color: var(--color-white);
      color: var(--color-red-600);
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-gray-100);
        }
      }
    }
    &.secondary {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-white);
      background-color: transparent;
      color: var(--color-white);
      &:hover {
        @media (hover: hover) {
          background-color: color-mix(in srgb, #ffffff 10%, transparent);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
          }
        }
      }
    }
  }
  @media (max-width: 640px) {
    .update-text {
      margin-bottom: calc(var(--spacing) * 2);
      text-align: center;
    }
    .update-actions {
      justify-content: center;
    }
  }
}
.install-button {
  position: fixed;
  right: calc(var(--spacing) * 5);
  bottom: calc(var(--spacing) * 20);
  z-index: 40;
  display: flex;
  align-items: center;
  border-radius: calc(infinity * 1px);
  background-color: var(--color-red-600);
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 2);
  color: var(--color-white);
  --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  i {
    margin-right: calc(var(--spacing) * 2);
  }
  &:hover {
    background-color: var(--color-red-700);
  }
}
@layer base {
  .tooltip-arrow,.tooltip-arrow:before {
    position: absolute;
    width: 8px;
    height: 8px;
    background: inherit;
  }
  .tooltip-arrow {
    visibility: hidden;
  }
  .tooltip-arrow:before {
    content: "";
    visibility: visible;
    transform: rotate(45deg);
  }
  [data-tooltip-style^='light'] + .tooltip > .tooltip-arrow:before {
    border-style: solid;
    border-color: var(--color-gray-200);
  }
  [data-tooltip-style^='light'] + .tooltip[data-popper-placement^='top'] > .tooltip-arrow:before {
    border-bottom-width: 1px;
    border-right-width: 1px;
  }
  [data-tooltip-style^='light'] + .tooltip[data-popper-placement^='right'] > .tooltip-arrow:before {
    border-bottom-width: 1px;
    border-left-width: 1px;
  }
  [data-tooltip-style^='light'] + .tooltip[data-popper-placement^='bottom'] > .tooltip-arrow:before {
    border-top-width: 1px;
    border-left-width: 1px;
  }
  [data-tooltip-style^='light'] + .tooltip[data-popper-placement^='left'] > .tooltip-arrow:before {
    border-top-width: 1px;
    border-right-width: 1px;
  }
  .tooltip[data-popper-placement^='top'] > .tooltip-arrow {
    bottom: -4px;
  }
  .tooltip[data-popper-placement^='bottom'] > .tooltip-arrow {
    top: -4px;
  }
  .tooltip[data-popper-placement^='left'] > .tooltip-arrow {
    right: -4px;
  }
  .tooltip[data-popper-placement^='right'] > .tooltip-arrow {
    left: -4px;
  }
  .tooltip.invisible > .tooltip-arrow:before {
    visibility: hidden;
  }
  [data-popper-arrow],[data-popper-arrow]:before {
    position: absolute;
    width: 8px;
    height: 8px;
    background: inherit;
  }
  [data-popper-arrow] {
    visibility: hidden;
  }
  [data-popper-arrow]:before {
    content: "";
    visibility: visible;
    transform: rotate(45deg);
  }
  [data-popper-arrow]:after {
    content: "";
    visibility: visible;
    transform: rotate(45deg);
    position: absolute;
    width: 9px;
    height: 9px;
    background: inherit;
  }
  [role="tooltip"] > [data-popper-arrow]:before {
    border-style: solid;
    border-color: var(--color-gray-200);
  }
  .dark [role="tooltip"] > [data-popper-arrow]:before {
    border-style: solid;
    border-color: var(--color-gray-600);
  }
  [role="tooltip"] > [data-popper-arrow]:after {
    border-style: solid;
    border-color: var(--color-gray-200);
  }
  .dark [role="tooltip"] > [data-popper-arrow]:after {
    border-style: solid;
    border-color: var(--color-gray-600);
  }
  [data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:before {
    border-bottom-width: 1px;
    border-right-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:after {
    border-bottom-width: 1px;
    border-right-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:before {
    border-bottom-width: 1px;
    border-left-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:after {
    border-bottom-width: 1px;
    border-left-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:before {
    border-top-width: 1px;
    border-left-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:after {
    border-top-width: 1px;
    border-left-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:before {
    border-top-width: 1px;
    border-right-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:after {
    border-top-width: 1px;
    border-right-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow] {
    bottom: -5px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow] {
    top: -5px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow] {
    right: -5px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow] {
    left: -5px;
  }
  [role="tooltip"].invisible > [data-popper-arrow]:before {
    visibility: hidden;
  }
  [role="tooltip"].invisible > [data-popper-arrow]:after {
    visibility: hidden;
  }
}
@layer base {
  [type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
    appearance: none;
    background-color: #fff;
    border-color: var(--color-gray-500);
    border-width: 1px;
    border-radius: 0px;
    padding-top: 0.5rem;
    padding-right: 0.75rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
    --tw-shadow: 0 0 #0000;
    &:focus {
      outline: 2px solid transparent;
      outline-offset: 2px;
      --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-color: var(--color-blue-600);
      --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
      box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      border-color: var(--color-blue-600);
    }
  }
  input::placeholder,textarea::placeholder {
    color: var(--color-gray-500);
    opacity: 1;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  input[type="time"]::-webkit-calendar-picker-indicator {
    background: none;
  }
  select:not([size]) {
    background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 10 6'%3e %3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 1 4 4 4-4'/%3e %3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 0.75em 0.75em;
    padding-right: 2.5rem;
    print-color-adjust: exact;
  }
  :is([dir=rtl]) select:not([size]) {
    background-position: left 0.75rem center;
    padding-right: 0.75rem;
    padding-left: 0;
  }
  [multiple] {
    background-image: initial;
    background-position: initial;
    background-repeat: unset;
    background-size: initial;
    padding-right: 0.75rem;
    print-color-adjust: unset;
  }
  [type='checkbox'],[type='radio'] {
    appearance: none;
    padding: 0;
    print-color-adjust: exact;
    display: inline-block;
    vertical-align: middle;
    background-origin: border-box;
    user-select: none;
    flex-shrink: 0;
    height: 1rem;
    width: 1rem;
    color: var(--color-blue-600);
    background-color: #fff;
    border-color: --color-gray-500;
    border-width: 1px;
    --tw-shadow: 0 0 #0000;
  }
  [type='checkbox'] {
    border-radius: 0px;
  }
  [type='radio'] {
    border-radius: 100%;
  }
  [type='checkbox']:focus,[type='radio']:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: var(--color-blue-600);
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  [type='checkbox']:checked,[type='radio']:checked,.dark [type='checkbox']:checked,.dark [type='radio']:checked {
    border-color: transparent !important;
    background-color: currentColor !important;
    background-size: 0.55em 0.55em;
    background-position: center;
    background-repeat: no-repeat;
  }
  [type='checkbox']:checked {
    background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 12'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M1 5.917 5.724 10.5 15 1.5'/%3e %3c/svg%3e");
    background-repeat: no-repeat;
    background-size: 0.55em 0.55em;
    print-color-adjust: exact;
  }
  [type='radio']:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
    background-size: 1em 1em;
  }
  .dark [type='radio']:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
    background-size: 1em 1em;
  }
  [type='checkbox']:indeterminate {
    background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 12'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M0.5 6h14'/%3e %3c/svg%3e");
    background-color: currentColor !important;
    border-color: transparent !important;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 0.55em 0.55em;
    print-color-adjust: exact;
  }
  [type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
    border-color: transparent !important;
    background-color: currentColor !important;
  }
  [type='file'] {
    background: unset;
    border-color: inherit;
    border-width: 0;
    border-radius: 0;
    padding: 0;
    font-size: unset;
    line-height: inherit;
  }
  [type='file']:focus {
    outline: 1px auto inherit;
  }
  input[type=file]::file-selector-button {
    color: white;
    background: var(--color-gray-800);
    border: 0;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
    padding-left: 2rem;
    padding-right: 1rem;
    margin-inline-start: -1rem;
    margin-inline-end: 1rem;
    &:hover {
      background: var(--color-gray-700);
    }
  }
  :is([dir=rtl]) input[type=file]::file-selector-button {
    padding-right: 2rem;
    padding-left: 1rem;
  }
  .dark input[type=file]::file-selector-button {
    color: white;
    background: var(--color-gray-600);
    &:hover {
      background: var(--color-gray-500);
    }
  }
  input[type="range"]::-webkit-slider-thumb {
    height: 1.25rem;
    width: 1.25rem;
    background: var(--color-blue-600);
    border-radius: 9999px;
    border: 0;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
  }
  input[type="range"]:disabled::-webkit-slider-thumb {
    background: var(--color-gray-400);
  }
  .dark input[type="range"]:disabled::-webkit-slider-thumb {
    background: var(--color-gray-500);
  }
  input[type="range"]:focus::-webkit-slider-thumb {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity));
  }
  input[type="range"]::-moz-range-thumb {
    height: 1.25rem;
    width: 1.25rem;
    background: var(--color-blue-600);
    border-radius: 9999px;
    border: 0;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
  }
  input[type="range"]:disabled::-moz-range-thumb {
    background: var(--color-gray-400);
  }
  .dark input[type="range"]:disabled::-moz-range-thumb {
    background: var(--color-gray-500);
  }
  input[type="range"]::-moz-range-progress {
    background: var(--color-blue-500);
  }
  input[type="range"]::-ms-fill-lower {
    background: var(--color-blue-500);
  }
  input[type="range"].range-sm::-webkit-slider-thumb {
    height: 1rem;
    width: 1rem;
  }
  input[type="range"].range-lg::-webkit-slider-thumb {
    height: 1.5rem;
    width: 1.5rem;
  }
  input[type="range"].range-sm::-moz-range-thumb {
    height: 1rem;
    width: 1rem;
  }
  input[type="range"].range-lg::-moz-range-thumb {
    height: 1.5rem;
    width: 1.5rem;
  }
  .toggle-bg:after {
    content: "";
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    background: white;
    border-color: var(--color-gray-300);
    border-width: 1px;
    border-radius: 9999px;
    height: 1.25rem;
    width: 1.25rem;
    transition-property: background-color,border-color,color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;
    transition-duration: .15s;
    box-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  }
  input:checked + .toggle-bg:after {
    transform: translateX(100%);;
    border-color: white;
  }
  input:checked + .toggle-bg {
    background: var(--color-blue-600);
    border-color: var(--color-blue-600);
  }
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-font-weight: initial;
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}
