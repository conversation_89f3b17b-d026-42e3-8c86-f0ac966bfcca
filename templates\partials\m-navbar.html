<div id="m-NavContainer">
  <nav>
    <div class="menu-grid">
      <a href="/" class="nav-item" aria-label="Início">
        <i class="fas fa-home"></i>
      </a>

      <button
        data-drawer-target="menu-drawer"
        data-drawer-show="menu-drawer"
        data-drawer-placement="left"
        class="nav-item"
      >
        <i class="fas fa-compass"></i>
      </button>

      <button
        data-drawer-target="social-drawer"
        data-drawer-show="social-drawer"
        data-drawer-placement="left"
        class="nav-item"
      >
        <i class="fas fa-share-nodes"></i>
      </button>

      <button
        data-drawer-target="config-drawer"
        data-drawer-show="config-drawer"
        data-drawer-placement="right"
        class="nav-item"
      >
        <i class="fas fa-sliders"></i>
      </button>
    </div>
  </nav>

  <div
    id="menu-drawer"
    class="fixed top-0 left-0 z-[51] h-screen p-4 overflow-y-auto transition-transform -translate-x-full bg-[var(--background-secondary)] w-80"
    tabindex="-1"
    aria-labelledby="drawer-label"
  >
    <div class="drawer-header">
      <h1 id="drawer-label">Menu</h1>
      <button
        type="button"
        data-drawer-hide="menu-drawer"
        class="drawer-close"
        aria-controls="menu-drawer"
      >
        <i class="fas fa-xmark"></i>
      </button>
    </div>
    <div class="drawer-content">
      <ul>
        <li>
          <a href="/" class="drawer-link">
            <i class="fas fa-home"></i>
            <span data-translate="Homepage"></span>
          </a>
        </li>
        <li>
          <a href="/streaming" class="drawer-link">
            <i class="fas fa-play"></i>
            <span data-translate="Streaming"></span>
          </a>
        </li>
        <li>
          <a href="/about" class="drawer-link">
            <i class="fas fa-info-circle"></i>
            <span data-translate="About"></span>
          </a>
        </li>
        <li>
          <a href="/terms" class="drawer-link">
            <i class="fas fa-file-contract"></i>
            <span data-translate="tos"></span>
          </a>
        </li>
        <li>
          <a href="/privacy" class="drawer-link">
            <i class="fas fa-shield-halved"></i>
            <span data-translate="privacy"></span>
          </a>
        </li>
<li>
          <a href="/updates" class="drawer-link">
            <i class="fas fa-bell"></i>
            <span data-translate="updates"></span>
          </a>
        </li>
      </ul>
    </div>
  </div>

  <div
    id="social-drawer"
    class="fixed top-0 left-0 z-[51] h-screen p-4 overflow-y-auto transition-transform -translate-x-full bg-[var(--background-secondary)] w-80"
    tabindex="-1"
    aria-labelledby="drawer-label"
  >
    <div class="drawer-header">
      <h1 data-translate="followMe"></h1>
      <button
        type="button"
        data-drawer-hide="social-drawer"
        aria-controls="social-drawer"
        class="drawer-close"
      >
        <i class="fas fa-xmark"></i>
      </button>
    </div>
    <div class="drawer-content">
      <div id="social-icons">
        <a
          href="https://twitter.com/iamshiuba"
          target="_blank"
          rel="noopener"
          aria-label="Twitter"
          class="fa-brands fa-x-twitter"
        >
        </a>
        <a
          href="https://soundcloud.com/iamshiuba"
          target="_blank"
          rel="noopener"
          aria-label="SoundCloud"
          class="fa-brands fa-soundcloud"
        >
        </a>
        <a
          href="https://youtube.com/@iamshiuba"
          target="_blank"
          rel="noopener"
          aria-label="YouTube"
          class="fa-brands fa-youtube"
        >
        </a>
      </div>
    </div>
  </div>

  <div
    id="config-drawer"
    class="fixed top-0 right-0 z-[51] h-screen p-4 overflow-y-auto transition-transform translate-x-full bg-[var(--background-secondary)] w-80"
    tabindex="-1"
    aria-labelledby="drawer-label"
  >
    <div class="drawer-header">
      <h1 data-translate="config"></h1>
      <button
        type="button"
        data-drawer-hide="config-drawer"
        aria-controls="config-drawer"
        class="drawer-close"
      >
        <i class="fas fa-xmark"></i>
      </button>
    </div>
    <div class="drawer-content">
      <div id="theme-container">
        <h5><span data-translate="theme"></span></h5>
        <div class="flex justify-start w-20">
          <button
            data-theme-toggle
            data-theme-value="light"
            title="Light"
            aria-label="Light"
            class="theme-button mr-2.5"
          >
            <i class="fas fa-sun"></i>
          </button>
          <button
            data-theme-toggle
            data-theme-value="dark"
            title="Dark"
            aria-label="Dark"
            class="theme-button mr-2.5"
          >
            <i class="fas fa-moon"></i>
          </button>
          <button
            data-theme-toggle
            data-theme-value="black"
            title="Black"
            aria-label="Black"
            class="theme-button mr-2.5"
          >
            <i class="fas fa-lightbulb"></i>
          </button>
          <button
            data-theme-toggle
            data-theme-value="red"
            title="Red"
            aria-label="Red"
            class="theme-button"
          >
            <i class="fas fa-heart"></i>
          </button>
        </div>
      </div>
      <div id="language-container">
        <h5 data-translate="Translations"></h5>
        <ul id="language" aria-labelledby="language-label" role="group">
          <li class="langItem">
            <a
              data-language="en-US"
              title="English"
              class="fi fi-us"
              aria-label="English"
              ><p>en-US</p></a
            >
          </li>
          <li class="langItem">
            <a
              data-language="pt-BR"
              title="Português"
              class="fi fi-br"
              aria-label="Português"
              ><p>pt-BR</p></a
            >
          </li>
          <li class="langItem">
            <a
              data-language="jp-JP"
              title="日本語"
              class="fi fi-jp"
              aria-label="日本語"
            >
              <p>jp-JP</p></a
            >
          </li>
          <li class="langItem">
            <a
              data-language="ru-RU"
              title="Русский"
              class="fi fi-ru"
              aria-label="Русский"
              ><p>ru-RU</p></a
            >
          </li>
          <li class="langItem">
            <a
              data-language="hi-IN"
              title="हिन्दी"
              class="fi fi-in"
              aria-label="हिन्दी"
              ><p>hi-IN</p></a
            >
          </li>
          <li class="langItem">
            <a data-language="zh-CN" title="中文" class="fi fi-cn"
              ><p>zh-CN</p></a
            >
          </li>
        </ul>
      </div>
      <h5 lang="pt-BR">Versão: <b class="text-sm">v3.2.25</b></h5>
    </div>
  </div>
</div>
