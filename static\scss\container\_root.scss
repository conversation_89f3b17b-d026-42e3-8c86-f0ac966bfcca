@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

:root,
[data-theme="light"] {
    scrollbar-color: var(--color-gray-950) var(--color-gray-100);
    scrollbar-width: thin;
    @apply overflow-auto scroll-smooth;
    --background-primary: var(--color-gray-100);
    --background-secondary: var(--color-gray-200);
    --text-primary: var(--color-gray-950);
    --text-secondary: var(--color-gray-600);
    --accent-color: var(--is-red-50);
    --border-color: var(--color-gray-200);
    --shadow-color: hsla(0, 50%, 80%, 0.3);

    --is-red-95: oklch(0.95 0.02 29);
    --is-red-90: oklch(0.90 0.04 29);
    --is-red-85: oklch(0.85 0.06 29);
    --is-red-80: oklch(0.80 0.08 29);
    --is-red-75: oklch(0.75 0.10 29);
    --is-red-70: oklch(0.70 0.12 29);
    --is-red-65: oklch(0.65 0.14 29);
    --is-red-60: oklch(0.60 0.16 29);
    --is-red-55: oklch(0.55 0.17 29);
    --is-red-50: oklch(0.63 0.18 29);
    --is-red-45: oklch(0.58 0.17 29);
    --is-red-40: oklch(0.53 0.16 29);
    --is-red-35: oklch(0.48 0.14 29);
    --is-red-30: oklch(0.43 0.12 29);
    --is-red-25: oklch(0.38 0.10 29);
    --is-red-20: oklch(0.33 0.08 29);
    --is-red-15: oklch(0.28 0.06 29);
    --is-red-10: oklch(0.23 0.04 29);
    --is-red-5: oklch(0.18 0.02 29);
}

[data-theme="dark"] {
    scrollbar-color: var(--color-gray-300) var(--color-gray-950);
    --background-primary: var(--color-gray-950);
    --background-secondary: var(--color-gray-900);
    --text-primary: var(--color-gray-100);
    --text-secondary: var(--color-gray-300);
    --accent-color: var(--is-red-40);
    --border-color: var(--color-gray-900);
    --shadow-color: hsla(240, 100%, 20%, 0.3);
}

[data-theme="black"] {
    scrollbar-color: white black;
    --background-primary: black;
    --background-secondary: var(--color-neutral-950);
    --text-primary: white;
    --text-secondary: var(--color-neutral-300);
    --accent-color: var(--is-red-50);
    --border-color:  var(--color-neutral-900);
    --shadow-color: hsla(0, 100%, 15%, 0.3);
}

[data-theme="red"] {
    scrollbar-color: var(--is-red-70) var(--is-red-15);
    --background-primary: var(--is-red-15);
    --background-secondary: var(--is-red-20);
    --text-primary: var(--is-red-90);
    --text-secondary: var(--is-red-70);
    --accent-color: var(--is-red-50);
    --border-color: var(--is-red-25);
    --shadow-color: hsla(0, 100%, 27%, 0.2);
}

body {
    font-family: 'Poppins', sans-serif;
    @apply bg-[var(--background-primary)] text-[var(--text-primary)];
}

.active {
    @apply text-[var(--text-secondary)] font-bold;
}

@keyframes fade-in {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes slideRight {
    from {
        transform: translateX(-100%);
    }

    to {
        transform: translateX(0);
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }

    to {
        transform: translateY(0);
    }
}

@keyframes float {
    0% {
        transform: translatey(0px);
    }

    50% {
        transform: translatey(-20px);
    }

    100% {
        transform: translatey(0px);
    }
}